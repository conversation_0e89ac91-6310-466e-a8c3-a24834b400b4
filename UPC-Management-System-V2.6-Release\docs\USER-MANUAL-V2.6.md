# UPC管理系统 V2.6 用户手册

## 📖 目录
1. [系统概述](#系统概述)
2. [登录系统](#登录系统)
3. [仪表盘](#仪表盘)
4. [UPC码申请](#upc码申请)
5. [我的UPC记录](#我的upc记录)
6. [UPC池管理](#upc池管理)
7. [数据导入](#数据导入)
8. [系统设置](#系统设置)
9. [V2.6新功能](#v26新功能)
10. [常见问题](#常见问题)

## 系统概述

UPC管理系统V2.6是一个专业的UPC码管理平台，支持UPC码的申请、分配、回收和统计管理。V2.6版本新增了公司名称全局显示和自动更新功能，提供更好的用户体验。

### 主要功能
- **UPC码管理**: 完整的UPC码生命周期管理
- **用户权限**: 管理员和员工角色分离
- **数据统计**: 实时图表和报表分析
- **通知系统**: 邮件和短信通知支持
- **数据备份**: 自动和手动备份功能
- **公司信息**: 全局公司名称显示（V2.6新增）

## 登录系统

### 访问地址
- 默认地址：http://localhost:3001
- 支持桌面和移动设备访问

### 默认账户
- **管理员账户**：admin / admin123
- **首次登录**：建议立即修改密码

### 登录步骤
1. 打开浏览器访问系统地址
2. 输入用户名和密码
3. 点击"登录"按钮
4. 系统自动跳转到仪表盘

## 仪表盘

仪表盘是系统的主页面，提供系统概览和快速操作入口。

### 显示内容
- **📊 统计卡片**：总UPC码数、可用数量、已分配数量、回收数量
- **📈 趋势图表**：UPC码申请趋势、状态分布图
- **🏢 公司信息**：显示当前公司名称（V2.6新增）
- **⚡ 快速操作**：常用功能快捷入口

### 图表说明
- **申请趋势图**：显示最近7天的UPC码申请情况
- **状态分布图**：显示不同状态UPC码的比例
- **实时更新**：数据每次访问时自动刷新

## UPC码申请

员工和管理员都可以申请UPC码，系统会自动分配可用的UPC码。

### 申请流程
1. 点击侧边栏"UPC申请"
2. 填写申请信息：
   - **申请数量**：需要的UPC码数量
   - **申请原因**：说明申请用途
   - **备注信息**：可选的补充说明
3. 点击"提交申请"
4. 系统自动分配UPC码
5. 查看申请结果

### 申请限制
- **单次上限**：系统设置中可配置
- **权限控制**：根据用户角色限制申请数量
- **库存检查**：自动检查可用UPC码数量

### 申请状态
- **成功**：UPC码已分配，可在"我的UPC记录"中查看
- **失败**：库存不足或超出申请限制
- **处理中**：申请正在处理（如启用审批流程）

## 我的UPC记录

查看个人的UPC码申请历史和当前持有的UPC码。

### 记录信息
- **UPC码**：分配的具体UPC码
- **申请时间**：申请提交时间
- **状态**：当前状态（已分配/已回收/已使用）
- **申请原因**：申请时填写的原因
- **备注**：相关备注信息

### 操作功能
- **查看详情**：点击记录查看详细信息
- **导出数据**：导出个人UPC记录
- **状态筛选**：按状态筛选记录
- **搜索功能**：按UPC码或原因搜索

### 状态说明
- **已分配**：UPC码已分配给用户，可正常使用
- **已回收**：UPC码已被系统回收，不可再使用
- **已使用**：UPC码已被标记为使用状态

## UPC池管理

管理员专用功能，用于管理系统中的所有UPC码资源。

### 主要功能
- **码池概览**：查看所有UPC码的状态统计
- **批量操作**：批量回收、删除或修改UPC码
- **导入导出**：支持Excel格式的数据导入导出
- **状态管理**：修改UPC码状态和备注信息

### 状态管理
- **可用**：可以分配给用户的UPC码
- **已分配**：已分配给用户但未使用的UPC码
- **已回收**：从用户处回收的UPC码
- **无效**：标记为无效的UPC码

### 批量操作
1. 选择要操作的UPC码（支持全选）
2. 选择操作类型（回收/删除/修改状态）
3. 确认操作
4. 系统执行批量操作

## 数据导入

管理员可以通过数据导入功能批量导入UPC码数据。

### 支持格式
- **Excel文件**：.xlsx 和 .xls 格式
- **CSV文件**：逗号分隔值文件
- **JSON文件**：标准JSON格式

### 导入步骤
1. 点击"选择文件"按钮
2. 选择要导入的数据文件
3. 预览导入数据
4. 确认数据格式正确
5. 点击"开始导入"
6. 等待导入完成

### 数据格式要求
```
UPC码,状态,备注
123456789012,available,批量导入
123456789013,available,批量导入
```

### 导入结果
- **成功数量**：成功导入的记录数
- **失败数量**：导入失败的记录数
- **错误详情**：失败记录的具体错误信息

## 系统设置

管理员可以在系统设置中配置各种系统参数。

### 基本设置
- **系统名称**：自定义系统显示名称
- **公司名称**：设置公司名称，将在所有页面显示（V2.6新增）
- **时区设置**：设置系统时区

### UPC设置
- **默认前缀**：UPC码的默认前缀（可选）
- **申请上限**：单次申请的最大数量
- **自动回收**：设置自动回收规则

### 通知设置
- **邮件通知**：配置SMTP邮件服务器
- **短信通知**：配置短信服务提供商
- **通知规则**：设置何时发送通知

### 备份设置
- **自动备份**：启用/禁用自动备份
- **备份频率**：设置备份间隔（每日/每周/每月）
- **保留数量**：设置保留的备份文件数量

## V2.6新功能

### 公司名称全局显示
V2.6版本新增了公司名称全局显示功能，让系统更具企业特色。

#### 显示位置
- **所有页面头部**：每个功能页面都显示公司名称
- **侧边栏底部**：在版权信息中显示公司名称
- **登录页面**：在系统介绍中显示公司名称

#### 设置方法
1. 以管理员身份登录系统
2. 进入"系统设置"页面
3. 在"基本设置"中修改"公司名称"
4. 点击"保存基本设置"
5. 所有页面的公司名称立即更新

### 自动更新机制
V2.6版本实现了设置的自动更新机制，提供更好的用户体验。

#### 功能特点
- **即时生效**：修改设置后无需刷新页面
- **全局同步**：所有页面的显示内容自动同步
- **智能加载**：页面切换时自动获取最新设置

#### 工作原理
1. 用户在系统设置中修改公司名称
2. 系统保存设置到数据库
3. 当前页面立即应用新设置
4. 用户切换到其他页面时，系统自动加载最新设置
5. 所有显示位置保持一致

## 常见问题

### Q1: 如何修改管理员密码？
**A**: 登录后点击右上角用户名，选择"修改密码"，输入新密码并确认。

### Q2: UPC码申请失败怎么办？
**A**: 检查以下几点：
- 申请数量是否超出限制
- 系统中是否有足够的可用UPC码
- 网络连接是否正常

### Q3: 如何备份系统数据？
**A**: 
- **自动备份**：在系统设置中启用自动备份
- **手动备份**：运行 `scripts\backup.bat` 脚本

### Q4: 公司名称不显示怎么办？
**A**: 
1. 确认已在系统设置中配置公司名称
2. 刷新浏览器页面
3. 检查浏览器控制台是否有错误
4. 确认使用的是V2.6版本

### Q5: 如何导入大量UPC码数据？
**A**: 
1. 准备Excel或CSV格式的数据文件
2. 确保数据格式符合要求
3. 使用"数据导入"功能批量导入
4. 建议分批导入大量数据

### Q6: 系统运行缓慢怎么办？
**A**: 
- 检查服务器资源使用情况
- 清理过期的备份文件
- 重启系统服务
- 优化数据库（如有大量数据）

### Q7: 忘记密码怎么办？
**A**: 
- 联系系统管理员重置密码
- 或直接修改 `data/users.json` 文件中的密码字段

### Q8: 如何添加新用户？
**A**: 
1. 以管理员身份登录
2. 进入用户管理页面
3. 点击"添加用户"
4. 填写用户信息并设置权限
5. 保存用户信息

---

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. **查看本手册**：大部分问题都能在手册中找到解决方案
2. **检查系统日志**：查看控制台输出的错误信息
3. **联系管理员**：向系统管理员报告问题
4. **记录错误信息**：详细记录错误现象和操作步骤

---

*UPC管理系统 V2.6.0 用户手册 - 深圳速拓电子商务有限公司*
