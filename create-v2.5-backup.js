// UPC管理系统 V2.5.0 正式版备份脚本
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

const VERSION = 'V2.5.0';
const BUILD_DATE = new Date().toISOString().split('T')[0];
const BACKUP_NAME = `UPC-Management-System-${VERSION}-Release-${BUILD_DATE}`;

console.log(`🚀 开始创建 ${VERSION} 正式版备份...`);
console.log(`📦 备份名称: ${BACKUP_NAME}`);

// 创建备份目录
const backupDir = path.join(__dirname, 'backups');
if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
    console.log('📁 创建备份目录: backups/');
}

// 创建压缩文件
const output = fs.createWriteStream(path.join(backupDir, `${BACKUP_NAME}.zip`));
const archive = archiver('zip', {
    zlib: { level: 9 } // 最高压缩级别
});

// 监听事件
output.on('close', function() {
    const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
    console.log(`✅ 备份完成！`);
    console.log(`📊 备份大小: ${sizeInMB} MB`);
    console.log(`📍 备份位置: backups/${BACKUP_NAME}.zip`);
    console.log(`🎉 UPC管理系统 ${VERSION} 正式版备份创建成功！`);
});

output.on('end', function() {
    console.log('📝 数据写入完成');
});

archive.on('warning', function(err) {
    if (err.code === 'ENOENT') {
        console.warn('⚠️ 警告:', err);
    } else {
        throw err;
    }
});

archive.on('error', function(err) {
    console.error('❌ 备份失败:', err);
    throw err;
});

// 连接输出流
archive.pipe(output);

console.log('📋 添加核心文件...');

// 添加核心系统文件
const coreFiles = [
    'simple-server.js',
    'package.json',
    'package-lock.json',
    'V2.5-Release-Notes.md'
];

coreFiles.forEach(file => {
    if (fs.existsSync(file)) {
        archive.file(file, { name: file });
        console.log(`  ✓ ${file}`);
    } else {
        console.log(`  ⚠️ 跳过不存在的文件: ${file}`);
    }
});

console.log('📁 添加目录...');

// 添加目录（排除不需要的文件）
const directories = [
    { src: 'public/', dest: 'public/' },
    { src: 'data/', dest: 'data/' },
    { src: 'logs/', dest: 'logs/' },
    { src: 'services/', dest: 'services/' }
];

directories.forEach(dir => {
    if (fs.existsSync(dir.src)) {
        archive.directory(dir.src, dir.dest);
        console.log(`  ✓ ${dir.src}`);
    } else {
        console.log(`  ⚠️ 跳过不存在的目录: ${dir.src}`);
    }
});

// 创建版本信息文件
const versionInfo = {
    version: VERSION,
    buildDate: BUILD_DATE,
    releaseType: '正式版',
    description: 'UPC管理系统正式版，完整功能集成，支持自定义短信测试和实时系统监控',
    features: [
        '自定义短信测试功能',
        '实时系统信息监控',
        '真实邮件短信发送',
        '完整权限控制',
        '数据可视化',
        '自动备份功能'
    ],
    technicalStack: {
        frontend: 'HTML5 + CSS3 + JavaScript',
        backend: 'Node.js + Express.js',
        database: 'JSON文件存储',
        email: 'Nodemailer + SMTP',
        sms: '腾讯云SMS API',
        charts: 'Chart.js'
    },
    systemRequirements: {
        nodejs: '14.0+',
        memory: '512MB+',
        storage: '100MB+',
        network: 'SMTP + HTTP/HTTPS'
    },
    deployment: {
        install: 'npm install',
        start: 'npm start',
        access: 'http://localhost:3001'
    },
    support: {
        company: '深圳速拓电子商务有限公司',
        copyright: '© 2025 版权所有'
    }
};

archive.append(JSON.stringify(versionInfo, null, 2), { name: 'version-info.json' });
console.log('  ✓ version-info.json (版本信息)');

// 创建安装说明
const installGuide = `# UPC管理系统 V2.5.0 正式版 安装指南

## 🚀 快速安装

### 1. 系统要求
- Node.js 14.0 或更高版本
- 最低 512MB 内存
- 最低 100MB 可用存储空间

### 2. 安装步骤

\`\`\`bash
# 1. 解压备份文件到目标目录
unzip UPC-Management-System-V2.5.0-Release-${BUILD_DATE}.zip

# 2. 进入系统目录
cd UPC-Management-System-V2.5.0-Release-${BUILD_DATE}

# 3. 安装依赖包
npm install

# 4. 启动系统
npm start
\`\`\`

### 3. 访问系统
打开浏览器访问: http://localhost:3001

### 4. 默认账户
- 管理员: sutuo_admin / Sutuo@2025!
- 业务经理: manager / Manager@2025
- 操作员: operator / Operator@2025

## ⚙️ 配置说明

### 邮件服务配置
1. 进入系统设置 → 通知设置
2. 启用邮件服务
3. 配置SMTP服务器信息

### 短信服务配置
1. 进入系统设置 → 通知设置
2. 启用短信服务
3. 配置腾讯云SMS服务信息

## 📞 技术支持
如有问题请通过系统内置反馈功能联系技术支持。

---
UPC管理系统 V2.5.0 正式版
© 2025 深圳速拓电子商务有限公司 版权所有
`;

archive.append(installGuide, { name: 'INSTALL.md' });
console.log('  ✓ INSTALL.md (安装指南)');

// 完成打包
console.log('🔄 正在压缩文件...');
archive.finalize();
