# UPC管理系统 V2.5.0 正式版部署检查清单

## 📋 部署前检查

### 🖥️ 系统环境检查
- [ ] **Node.js版本**: 确认已安装 Node.js 14.0 或更高版本
- [ ] **内存要求**: 确认服务器至少有 512MB 可用内存
- [ ] **存储空间**: 确认至少有 100MB 可用存储空间
- [ ] **网络连接**: 确认服务器可以访问SMTP和HTTP/HTTPS服务
- [ ] **端口可用**: 确认端口 3001 可用（或配置其他端口）

### 📦 文件准备检查
- [ ] **备份文件**: 下载 `UPC-Management-System-V2.5.0-Release-2025-07-01.zip`
- [ ] **解压缩**: 将备份文件解压到目标目录
- [ ] **文件完整性**: 确认所有核心文件存在
  - [ ] `simple-server.js` (主服务器文件)
  - [ ] `package.json` (依赖配置)
  - [ ] `public/index.html` (前端页面)
  - [ ] `data/` 目录 (数据存储)

### 🔧 依赖安装检查
- [ ] **运行安装命令**: `npm install`
- [ ] **检查安装结果**: 确认所有依赖包安装成功
- [ ] **验证关键依赖**:
  - [ ] `express` (Web框架)
  - [ ] `nodemailer` (邮件服务)
  - [ ] `tencentcloud-sdk-nodejs` (短信服务)

## 🚀 部署步骤

### 1. 基础部署
```bash
# 1. 解压备份文件
unzip UPC-Management-System-V2.5.0-Release-2025-07-01.zip

# 2. 进入系统目录
cd UPC-Management-System-V2.5.0-Release-2025-07-01

# 3. 安装依赖
npm install

# 4. 启动系统
npm start
```

### 2. 验证部署
- [ ] **服务启动**: 确认看到启动成功信息
  ```
  🚀 UPC管理系统 V2.5 正式版已启动
  📍 访问地址: http://localhost:3001
  ```
- [ ] **Web访问**: 浏览器访问 `http://localhost:3001`
- [ ] **页面加载**: 确认首页正常显示
- [ ] **版本信息**: 确认页面显示 "V2.5 正式版"

## ⚙️ 配置检查

### 🔐 用户账户验证
- [ ] **管理员账户**: `sutuo_admin` / `Sutuo@2025!`
- [ ] **业务经理账户**: `manager` / `Manager@2025`
- [ ] **操作员账户**: `operator` / `Operator@2025`
- [ ] **登录测试**: 使用各账户测试登录功能

### 📧 邮件服务配置
- [ ] **进入设置**: 系统设置 → 通知设置 → 邮件服务
- [ ] **启用服务**: 勾选"启用邮件服务"
- [ ] **SMTP配置**:
  - [ ] SMTP服务器地址
  - [ ] SMTP端口 (通常587或465)
  - [ ] 发送邮箱账户
  - [ ] 邮箱授权码/密码
- [ ] **测试发送**: 点击"测试邮件发送"验证配置

### 📱 短信服务配置
- [ ] **进入设置**: 系统设置 → 通知设置 → 短信服务
- [ ] **启用服务**: 勾选"启用短信服务"
- [ ] **腾讯云配置**:
  - [ ] Access Key ID
  - [ ] Access Key Secret
  - [ ] 短信签名
  - [ ] 模板ID
  - [ ] 应用ID
- [ ] **测试发送**: 输入测试手机号，点击"测试短信发送"

### 🔔 库存预警配置
- [ ] **启用预警**: 勾选"启用库存预警"
- [ ] **设置阈值**: 配置库存预警阈值
- [ ] **通知方式**: 选择邮件和/或短信通知
- [ ] **接收人配置**:
  - [ ] 邮件接收人 (邮箱地址)
  - [ ] 短信接收人 (手机号码)

## 🧪 功能测试

### 🆕 V2.5 新功能测试

#### 1. 自定义短信测试
- [ ] **输入自定义手机号**: 在"测试手机号"输入框输入11位手机号
- [ ] **格式验证**: 测试输入错误格式，确认有错误提示
- [ ] **我的号码功能**: 点击"📱 我的号码"按钮测试快捷填充
- [ ] **记忆功能**: 重新打开页面，确认自动填充上次使用的号码
- [ ] **发送测试**: 点击"测试短信发送"，确认能发送到指定号码

#### 2. 实时系统信息
- [ ] **进入系统设置**: 查看系统信息部分
- [ ] **动态数据**: 确认显示实时的系统运行时间
- [ ] **内存监控**: 确认显示当前内存使用情况
- [ ] **数据统计**: 确认显示实时的数据库记录数量
- [ ] **服务状态**: 确认显示邮件、短信服务状态

### 🔄 核心功能测试
- [ ] **UPC申请**: 测试UPC码申请功能
- [ ] **库存查询**: 测试库存查询功能
- [ ] **用户管理**: 测试用户权限控制
- [ ] **数据统计**: 测试统计图表显示
- [ ] **系统备份**: 测试自动备份功能

## 🛡️ 安全检查

### 🔐 访问控制
- [ ] **未登录访问**: 确认未登录时无法访问系统功能
- [ ] **权限控制**: 确认不同角色有不同的功能权限
- [ ] **会话管理**: 测试登录会话超时机制

### 🔒 数据安全
- [ ] **输入验证**: 测试各种异常输入的处理
- [ ] **错误处理**: 确认错误信息不泄露敏感信息
- [ ] **数据备份**: 确认重要数据有备份机制

## 📊 性能检查

### ⚡ 响应性能
- [ ] **页面加载**: 首页加载时间 < 3秒
- [ ] **功能响应**: 各功能操作响应时间 < 2秒
- [ ] **数据查询**: 数据查询响应时间 < 1秒

### 💾 资源使用
- [ ] **内存使用**: 系统运行内存使用 < 100MB
- [ ] **CPU使用**: 空闲时CPU使用率 < 5%
- [ ] **存储空间**: 确认有足够的日志和数据存储空间

## 📱 兼容性检查

### 🌐 浏览器兼容
- [ ] **Chrome**: 测试最新版Chrome浏览器
- [ ] **Firefox**: 测试最新版Firefox浏览器
- [ ] **Edge**: 测试最新版Edge浏览器
- [ ] **Safari**: 测试Safari浏览器（如适用）

### 📱 设备兼容
- [ ] **桌面端**: 测试桌面电脑访问
- [ ] **平板端**: 测试平板设备访问
- [ ] **手机端**: 测试手机浏览器访问

## 🔧 故障排除

### 常见问题检查
- [ ] **端口冲突**: 如果3001端口被占用，修改端口配置
- [ ] **依赖缺失**: 重新运行 `npm install`
- [ ] **权限问题**: 确认文件读写权限正确
- [ ] **网络问题**: 检查防火墙和网络连接

### 日志检查
- [ ] **启动日志**: 检查系统启动时的日志信息
- [ ] **错误日志**: 查看 `logs/` 目录下的错误日志
- [ ] **访问日志**: 检查用户访问和操作日志

## ✅ 部署完成确认

### 最终验证
- [ ] **系统版本**: 确认显示 "V2.5.0 正式版"
- [ ] **所有功能**: 核心功能全部正常工作
- [ ] **新功能**: V2.5新功能全部可用
- [ ] **性能稳定**: 系统运行稳定，无异常错误
- [ ] **文档齐全**: 相关文档和说明完整

### 交付文档
- [ ] **安装指南**: `INSTALL.md`
- [ ] **版本说明**: `V2.5-Release-Notes.md`
- [ ] **功能对比**: `V2.5-Feature-Comparison.md`
- [ ] **部署清单**: `V2.5-Deployment-Checklist.md`

---

## 📞 技术支持

如在部署过程中遇到问题，请：
1. 检查本清单中的相关项目
2. 查看系统日志文件
3. 通过系统内置反馈功能联系技术支持

**UPC管理系统 V2.5.0 正式版**  
*企业级UPC码管理解决方案*  
© 2025 深圳速拓电子商务有限公司 版权所有
