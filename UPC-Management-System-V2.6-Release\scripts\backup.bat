@echo off
chcp 65001 >nul
echo.
echo ========================================
echo      UPC管理系统 V2.6 数据备份工具
echo ========================================
echo.

:: 获取当前日期时间
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

:: 创建备份目录
if not exist "data\backups" mkdir data\backups

:: 备份数据文件
echo [1/4] 备份用户数据...
if exist "data\users.json" (
    copy "data\users.json" "data\backups\users_%timestamp%.json" >nul
    echo ✅ 用户数据备份完成
) else (
    echo ⚠️  用户数据文件不存在
)

echo.
echo [2/4] 备份系统设置...
if exist "data\system_settings.json" (
    copy "data\system_settings.json" "data\backups\system_settings_%timestamp%.json" >nul
    echo ✅ 系统设置备份完成
) else (
    echo ⚠️  系统设置文件不存在
)

echo.
echo [3/4] 备份UPC数据...
if exist "data\upc_codes.json" (
    copy "data\upc_codes.json" "data\backups\upc_codes_%timestamp%.json" >nul
    echo ✅ UPC数据备份完成
) else (
    echo ⚠️  UPC数据文件不存在
)

echo.
echo [4/4] 清理旧备份文件...
:: 保留最近30个备份文件
for /f "skip=30 delims=" %%F in ('dir /b /o-d "data\backups\*.json" 2^>nul') do (
    del "data\backups\%%F" >nul 2>&1
)
echo ✅ 旧备份文件清理完成

echo.
echo ========================================
echo           🎉 备份完成！
echo ========================================
echo.
echo 📁 备份位置: data\backups\
echo 🕐 备份时间: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo 📋 备份文件:
if exist "data\backups\users_%timestamp%.json" echo    - users_%timestamp%.json
if exist "data\backups\system_settings_%timestamp%.json" echo    - system_settings_%timestamp%.json
if exist "data\backups\upc_codes_%timestamp%.json" echo    - upc_codes_%timestamp%.json
echo.

pause
