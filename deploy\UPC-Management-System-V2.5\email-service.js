// 邮件发送服务模块 - 支持真实发送
const nodemailer = require('nodemailer');
const fs = require('fs');

class EmailService {
    constructor() {
        this.transporter = null;
        this.config = null;
        this.loadConfig();
    }

    // 加载邮件配置
    loadConfig() {
        try {
            const settingsData = fs.readFileSync('system_settings.json', 'utf8');
            const settings = JSON.parse(settingsData);
            
            if (settings.notification && settings.notification.email) {
                this.config = settings.notification.email;
                this.initializeTransporter();
            }
        } catch (error) {
            console.log('📧 邮件配置加载失败:', error.message);
        }
    }

    // 初始化邮件传输器
    initializeTransporter() {
        if (!this.config || !this.config.enabled) {
            console.log('📧 邮件服务未启用');
            return;
        }

        try {
            // 判断安全连接类型
            let securityType = 'none';
            if (this.config.smtpSecurity) {
                if (typeof this.config.smtpSecurity === 'string') {
                    securityType = this.config.smtpSecurity.toLowerCase();
                } else if (typeof this.config.smtpSecurity === 'object') {
                    securityType = 'tls'; // 默认使用TLS
                }
            }
            const isSSL = securityType === 'ssl';
            const isTLS = securityType === 'tls';

            // 根据邮箱类型自动调整配置
            const emailDomain = this.config.senderEmail.split('@')[1];
            let smtpConfig = {
                host: this.config.smtpServer,
                port: parseInt(this.config.smtpPort),
                secure: isSSL,
                auth: {
                    user: this.config.senderEmail,
                    pass: this.config.emailPassword
                },
                connectionTimeout: 60000,
                greetingTimeout: 30000,
                socketTimeout: 60000,
                pool: true, // 使用连接池
                maxConnections: 1, // 最大连接数
                maxMessages: 3 // 每个连接最大消息数
            };

            // 针对不同邮箱服务商的特殊配置
            if (emailDomain === '163.com' || emailDomain === '126.com' || emailDomain === 'yeah.net') {
                // 163邮箱特殊配置
                smtpConfig.tls = {
                    rejectUnauthorized: false,
                    ciphers: 'SSLv3'
                };
                if (this.config.smtpPort == 587) {
                    smtpConfig.secure = false;
                    smtpConfig.requireTLS = true;
                }
            } else if (emailDomain === 'qq.com') {
                // QQ邮箱配置
                smtpConfig.tls = {
                    rejectUnauthorized: false
                };
            } else {
                // 其他邮箱的通用配置
                smtpConfig.tls = {
                    rejectUnauthorized: false
                };
            }

            this.transporter = nodemailer.createTransport(smtpConfig);

            console.log('📧 邮件服务初始化成功');
        } catch (error) {
            console.error('📧 邮件服务初始化失败:', error.message);
        }
    }

    // 发送邮件
    async sendEmail(to, subject, content, isHtml = false) {
        const startTime = Date.now();
        try {
            if (!this.config || !this.config.enabled) {
                throw new Error('邮件服务未启用');
            }

            if (!this.transporter) {
                this.initializeTransporter();
                if (!this.transporter) {
                    throw new Error('邮件传输器创建失败');
                }
            }

            const senderName = this.config.senderName || 'UPC管理系统';
            const mailOptions = {
                from: `"${senderName}" <${this.config.senderEmail}>`,
                to: to,
                subject: subject,
                [isHtml ? 'html' : 'text']: content
            };

            console.log('📧 开始发送邮件...');

            // 设置15秒总超时
            const sendPromise = this.transporter.sendMail(mailOptions);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('邮件发送超时(15秒)')), 15000)
            );

            const info = await Promise.race([sendPromise, timeoutPromise]);

            const duration = Date.now() - startTime;
            console.log(`📧 邮件发送成功: ${info.messageId} (耗时: ${duration}ms)`);

            return {
                success: true,
                messageId: info.messageId,
                recipient: to,
                subject: subject,
                duration: duration
            };
        } catch (error) {
            const duration = Date.now() - startTime;

            // 详细的错误分析
            let errorMessage = '邮件发送失败';
            let errorCode = error.code || 'UNKNOWN_ERROR';

            if (error.message.includes('超时')) {
                errorMessage = '邮件发送超时，可能是网络连接问题';
                errorCode = 'TIMEOUT';
            } else if (error.code === 'EAUTH') {
                errorMessage = '邮箱认证失败，请检查邮箱地址和授权码';
                errorCode = 'AUTH_FAILED';
            } else if (error.code === 'ECONNECTION') {
                errorMessage = 'SMTP服务器连接失败，请检查服务器地址和端口';
                errorCode = 'CONNECTION_FAILED';
            } else if (error.code === 'ESOCKET') {
                errorMessage = '网络连接中断，请检查网络设置';
                errorCode = 'SOCKET_ERROR';
            } else if (error.message.includes('getaddrinfo')) {
                errorMessage = 'DNS解析失败，无法找到SMTP服务器';
                errorCode = 'DNS_ERROR';
            } else if (error.message.includes('certificate')) {
                errorMessage = 'SSL证书验证失败，请检查加密设置';
                errorCode = 'SSL_ERROR';
            } else {
                errorMessage = `邮件发送失败: ${error.message}`;
            }

            console.error(`📧 邮件发送失败: ${errorMessage} (耗时: ${duration}ms)`);
            console.error('📧 错误详情:', {
                code: errorCode,
                message: error.message,
                stack: error.stack
            });

            return {
                success: false,
                message: errorMessage,
                error: errorCode,
                duration: duration,
                originalError: error.message
            };
        }
    }

    // 发送UPC申请通知邮件
    async sendUPCApplicationNotification(userEmail, userName, quantity, purpose, upcCodes) {
        const subject = `【UPC管理系统】UPC码申请成功通知`;
        
        const content = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #1890ff;">🎉 UPC码申请成功</h2>
            
            <p>尊敬的 <strong>${userName}</strong>，您好！</p>
            
            <p>您的UPC码申请已成功处理，详情如下：</p>
            
            <div style="background: #f6f8fa; padding: 15px; border-radius: 6px; margin: 15px 0;">
                <p><strong>申请数量：</strong>${quantity} 个</p>
                <p><strong>申请用途：</strong>${purpose}</p>
                <p><strong>申请时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
            </div>
            
            <h3>📋 分配的UPC码：</h3>
            <div style="background: #fff; border: 1px solid #e1e4e8; border-radius: 6px; padding: 15px;">
                ${upcCodes.map(code => `<div style="font-family: monospace; padding: 5px; border-bottom: 1px solid #f0f0f0;">${code}</div>`).join('')}
            </div>
            
            <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 6px;">
                <p style="margin: 0; color: #856404;">
                    <strong>📝 温馨提示：</strong><br>
                    • 请妥善保管您的UPC码<br>
                    • 如有疑问，请联系系统管理员<br>
                    • 您可以登录系统查看详细的申请记录
                </p>
            </div>
            
            <hr style="margin: 20px 0; border: none; border-top: 1px solid #e1e4e8;">
            
            <p style="color: #666; font-size: 12px;">
                此邮件由UPC管理系统自动发送，请勿回复。<br>
                发送时间：${new Date().toLocaleString('zh-CN')}
            </p>
        </div>
        `;

        return await this.sendEmail(userEmail, subject, content, true);
    }

    // 发送库存预警邮件
    async sendStockAlertEmail(recipients, currentStock, threshold) {
        const subject = `🚨 UPC码库存预警通知`;
        
        const shortageRate = ((threshold - currentStock) / threshold * 100).toFixed(1);
        
        const content = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #ff4d4f;">🚨 UPC码库存紧急预警</h2>
            
            <div style="background: #fff2f0; border: 1px solid #ffccc7; border-radius: 6px; padding: 20px; margin: 15px 0;">
                <h3 style="color: #cf1322; margin-top: 0;">库存告急！</h3>
                <p><strong>当前可用库存：</strong><span style="color: #cf1322; font-size: 18px;">${currentStock}</span> 个</p>
                <p><strong>预警阈值：</strong>${threshold} 个</p>
                <p><strong>库存不足率：</strong><span style="color: #cf1322;">${shortageRate}%</span></p>
            </div>
            
            <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px; padding: 15px; margin: 15px 0;">
                <h4 style="color: #389e0d; margin-top: 0;">🔧 建议操作：</h4>
                <ul style="color: #389e0d;">
                    <li>立即补充UPC码库存</li>
                    <li>检查UPC码使用情况</li>
                    <li>考虑调整预警阈值</li>
                    <li>联系相关供应商</li>
                </ul>
            </div>
            
            <hr style="margin: 20px 0; border: none; border-top: 1px solid #e1e4e8;">
            
            <p style="color: #666; font-size: 12px;">
                预警时间：${new Date().toLocaleString('zh-CN')}<br>
                此邮件由UPC管理系统自动发送，请及时处理。
            </p>
        </div>
        `;

        // 发送给多个收件人
        const recipientList = recipients.split(',').map(email => email.trim()).filter(email => email);
        const results = [];
        
        for (const recipient of recipientList) {
            try {
                const result = await this.sendEmail(recipient, subject, content, true);
                results.push({ recipient, success: true, messageId: result.messageId });
            } catch (error) {
                results.push({ recipient, success: false, error: error.message });
            }
        }
        
        return results;
    }

    // 测试邮件发送
    async sendTestEmail(testEmail) {
        const subject = '📧 UPC管理系统邮件服务测试';
        
        const content = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #1890ff;">📧 邮件服务测试</h2>
            
            <p>恭喜！您的邮件服务配置正确。</p>
            
            <div style="background: #f6f8fa; padding: 15px; border-radius: 6px; margin: 15px 0;">
                <p><strong>测试时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
                <p><strong>发送服务器：</strong>${this.config.smtpServer}:${this.config.smtpPort}</p>
                <p><strong>发送邮箱：</strong>${this.config.senderEmail}</p>
                <p><strong>加密方式：</strong>${this.config.smtpSecurity ? this.config.smtpSecurity.toString().toUpperCase() : 'NONE'}</p>
            </div>
            
            <p style="color: #52c41a;">✅ 邮件服务工作正常，可以正常发送通知邮件。</p>
            
            <hr style="margin: 20px 0; border: none; border-top: 1px solid #e1e4e8;">
            
            <p style="color: #666; font-size: 12px;">
                此邮件由UPC管理系统自动发送，用于测试邮件服务配置。
            </p>
        </div>
        `;

        return await this.sendEmail(testEmail, subject, content, true);
    }

    // 重新加载配置
    reloadConfig() {
        this.loadConfig();
    }
}

module.exports = EmailService;
