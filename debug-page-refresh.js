const fs = require('fs');

console.log('🔍 调试页面刷新问题');
console.log('==================');

// 检查当前的页面加载逻辑
function analyzePageLoadLogic() {
    console.log('\n1. 分析页面加载逻辑...');
    
    const content = fs.readFileSync('public/index.html', 'utf8');
    
    // 检查window.load事件处理
    const windowLoadMatch = content.match(/window\.addEventListener\('load'[\s\S]*?\}\);/);
    if (windowLoadMatch) {
        const windowLoadCode = windowLoadMatch[0];
        console.log('   ✅ 找到window.load事件处理器');
        
        // 检查关键逻辑
        const hasUserDataCheck = windowLoadCode.includes('localStorage.getItem(\'upc_user_data\')');
        const hasPagePreservation = windowLoadCode.includes('页面刷新，保持当前页面');
        const hasErrorHandling = windowLoadCode.includes('catch (e)');
        
        console.log(`   📊 关键逻辑检查:`);
        console.log(`      用户数据检查: ${hasUserDataCheck ? '✅' : '❌'}`);
        console.log(`      页面保持逻辑: ${hasPagePreservation ? '✅' : '❌'}`);
        console.log(`      异常处理: ${hasErrorHandling ? '✅' : '❌'}`);
        
        // 检查是否有可能导致登出的逻辑
        const hasLogoutCall = windowLoadCode.includes('logout()');
        const hasDataClear = windowLoadCode.includes('localStorage.removeItem(\'upc_user_data\')');
        const hasLoginRedirect = windowLoadCode.includes('showLogin()');
        
        console.log(`   🚨 潜在问题检查:`);
        console.log(`      包含logout调用: ${hasLogoutCall ? '❌ 有问题' : '✅ 正常'}`);
        console.log(`      清除用户数据: ${hasDataClear ? '⚠️ 在异常处理中' : '✅ 正常'}`);
        console.log(`      重定向到登录: ${hasLoginRedirect ? '⚠️ 在异常处理中' : '✅ 正常'}`);
        
        return !hasLogoutCall;
    } else {
        console.log('   ❌ 未找到window.load事件处理器');
        return false;
    }
}

// 检查DOMContentLoaded事件
function analyzeDOMContentLoaded() {
    console.log('\n2. 分析DOMContentLoaded事件...');
    
    const content = fs.readFileSync('public/index.html', 'utf8');
    
    const domLoadMatch = content.match(/document\.addEventListener\('DOMContentLoaded'[\s\S]*?\}\);/);
    if (domLoadMatch) {
        const domLoadCode = domLoadMatch[0];
        console.log('   ✅ 找到DOMContentLoaded事件处理器');
        
        // 检查是否有冲突的页面显示逻辑
        const hasShowDashboard = domLoadCode.includes('showDashboard(');
        const hasShowWelcome = domLoadCode.includes('showWelcomePage()');
        const hasShowLogin = domLoadCode.includes('showLogin()');
        const hasHandleLogin = domLoadCode.includes('handleLogin(fakeEvent)');
        
        console.log(`   📊 冲突逻辑检查:`);
        console.log(`      显示仪表盘: ${hasShowDashboard ? '❌ 有冲突' : '✅ 正常'}`);
        console.log(`      显示欢迎页: ${hasShowWelcome ? '❌ 有冲突' : '✅ 正常'}`);
        console.log(`      显示登录页: ${hasShowLogin ? '❌ 有冲突' : '✅ 正常'}`);
        console.log(`      自动登录: ${hasHandleLogin ? '❌ 有冲突' : '✅ 正常'}`);
        
        const hasConflicts = hasShowDashboard || hasShowWelcome || hasShowLogin || hasHandleLogin;
        
        if (hasConflicts) {
            console.log('   🚨 发现冲突逻辑！这可能导致页面刷新问题');
            
            // 显示冲突的代码片段
            if (hasShowDashboard) {
                const dashboardMatch = domLoadCode.match(/showDashboard\([^)]*\)/);
                if (dashboardMatch) {
                    console.log(`      冲突代码: ${dashboardMatch[0]}`);
                }
            }
            if (hasShowWelcome) {
                console.log(`      冲突代码: showWelcomePage()`);
            }
            if (hasShowLogin) {
                console.log(`      冲突代码: showLogin()`);
            }
            if (hasHandleLogin) {
                console.log(`      冲突代码: handleLogin(fakeEvent)`);
            }
        }
        
        return !hasConflicts;
    } else {
        console.log('   ❌ 未找到DOMContentLoaded事件处理器');
        return false;
    }
}

// 检查可能的自动执行代码
function checkAutoExecuteCode() {
    console.log('\n3. 检查自动执行代码...');
    
    const content = fs.readFileSync('public/index.html', 'utf8');
    
    // 检查是否有立即执行的函数调用
    const immediateCallPatterns = [
        /\n\s*logout\(\);/g,
        /\n\s*showLogin\(\);/g,
        /\n\s*showWelcomePage\(\);/g,
        /\n\s*location\.reload\(\);/g,
        /\n\s*localStorage\.removeItem\('upc_user_data'\);/g
    ];
    
    let hasImmediateCalls = false;
    immediateCallPatterns.forEach((pattern, index) => {
        const matches = content.match(pattern);
        if (matches) {
            const names = ['logout()', 'showLogin()', 'showWelcomePage()', 'location.reload()', 'localStorage.removeItem(\'upc_user_data\')'];
            console.log(`   ⚠️ 发现立即执行的 ${names[index]} 调用: ${matches.length} 次`);
            hasImmediateCalls = true;
        }
    });
    
    if (!hasImmediateCalls) {
        console.log('   ✅ 未发现立即执行的问题代码');
    }
    
    return !hasImmediateCalls;
}

// 检查可能的竞态条件
function checkRaceConditions() {
    console.log('\n4. 检查竞态条件...');
    
    const content = fs.readFileSync('public/index.html', 'utf8');
    
    // 检查是否有多个地方同时处理用户状态
    const userDataAccessPatterns = [
        /localStorage\.getItem\('upc_user_data'\)/g,
        /localStorage\.setItem\('upc_user_data'/g,
        /localStorage\.removeItem\('upc_user_data'\)/g,
        /currentUser\s*=/g
    ];
    
    console.log('   📊 用户状态访问统计:');
    userDataAccessPatterns.forEach((pattern, index) => {
        const matches = content.match(pattern);
        const names = ['读取用户数据', '保存用户数据', '清除用户数据', '设置currentUser'];
        const count = matches ? matches.length : 0;
        console.log(`      ${names[index]}: ${count} 次`);
    });
    
    // 检查是否有异步操作可能影响用户状态
    const asyncPatterns = [
        /fetch\([^)]*auth[^)]*\)/g,
        /fetch\([^)]*login[^)]*\)/g,
        /fetch\([^)]*logout[^)]*\)/g
    ];
    
    console.log('   📊 异步认证操作:');
    asyncPatterns.forEach((pattern, index) => {
        const matches = content.match(pattern);
        const names = ['认证请求', '登录请求', '登出请求'];
        const count = matches ? matches.length : 0;
        console.log(`      ${names[index]}: ${count} 次`);
    });
    
    return true;
}

// 生成调试建议
function generateDebugSuggestions(results) {
    console.log('\n📋 调试结果总结');
    console.log('================');
    
    const allPassed = Object.values(results).every(r => r);
    
    if (allPassed) {
        console.log('🤔 代码逻辑看起来正常，问题可能在其他地方...');
        
        console.log('\n💡 建议的调试步骤:');
        console.log('1. 🌐 在浏览器中打开开发者工具');
        console.log('2. 📱 访问 http://localhost:3001');
        console.log('3. 🔐 登录系统 (sutuo_admin / Sutuo@2025!)');
        console.log('4. 📄 进入任意功能页面');
        console.log('5. 🔍 在Console标签中查看日志');
        console.log('6. 🔄 按F5刷新页面');
        console.log('7. 👀 观察Console中的输出');
        
        console.log('\n🔍 关键日志信息:');
        console.log('   - "页面刷新，保持当前页面: xxx" - 应该出现');
        console.log('   - "用户信息解析失败" - 不应该出现');
        console.log('   - "没有用户信息" - 不应该出现');
        console.log('   - localStorage中的upc_user_data - 应该存在');
        
        console.log('\n🛠️ 可能的原因:');
        console.log('   1. 浏览器缓存问题 - 尝试硬刷新 (Ctrl+F5)');
        console.log('   2. localStorage被其他代码清除');
        console.log('   3. 网络请求失败导致状态丢失');
        console.log('   4. 浏览器兼容性问题');
        console.log('   5. 服务器端会话管理问题');
        
    } else {
        console.log('❌ 发现代码问题:');
        Object.entries(results).forEach(([key, passed]) => {
            if (!passed) {
                const names = {
                    pageLoadLogic: '页面加载逻辑',
                    domContentLoaded: 'DOMContentLoaded事件',
                    autoExecuteCode: '自动执行代码',
                    raceConditions: '竞态条件'
                };
                console.log(`   ❌ ${names[key]}存在问题`);
            }
        });
    }
    
    console.log('\n🚀 立即测试:');
    console.log('   访问: http://localhost:3001');
    console.log('   账号: sutuo_admin');
    console.log('   密码: Sutuo@2025!');
    
    return allPassed;
}

// 主调试函数
function runDebugAnalysis() {
    console.log('🚀 开始页面刷新问题调试分析...\n');
    
    const results = {
        pageLoadLogic: analyzePageLoadLogic(),
        domContentLoaded: analyzeDOMContentLoaded(),
        autoExecuteCode: checkAutoExecuteCode(),
        raceConditions: checkRaceConditions()
    };
    
    const success = generateDebugSuggestions(results);
    
    if (!success) {
        console.log('\n🔧 需要修复代码中发现的问题！');
    } else {
        console.log('\n✅ 代码分析完成，建议进行浏览器测试！');
    }
    
    return success;
}

// 运行调试分析
const success = runDebugAnalysis();
process.exit(success ? 0 : 1);
