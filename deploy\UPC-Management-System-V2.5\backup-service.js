// 自动备份服务模块
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const cron = require('node-cron');

class BackupService {
    constructor() {
        this.config = null;
        this.cronJob = null;
        this.loadConfig();
        this.initializeSchedule();
    }

    // 加载备份配置
    loadConfig() {
        try {
            const settingsData = fs.readFileSync('system_settings.json', 'utf8');
            const settings = JSON.parse(settingsData);
            
            if (settings.backup) {
                this.config = {
                    backupFrequency: settings.backup.backupFrequency || 'daily',
                    backupRetention: parseInt(settings.backup.backupRetention) || 30,
                    backupPath: settings.backup.backupPath || './backups'
                };
                console.log('💾 备份服务配置加载成功');
            } else {
                // 默认配置
                this.config = {
                    backupFrequency: 'daily',
                    backupRetention: 30,
                    backupPath: './backups'
                };
            }
        } catch (error) {
            console.log('💾 备份配置加载失败，使用默认配置:', error.message);
            this.config = {
                backupFrequency: 'daily',
                backupRetention: 30,
                backupPath: './backups'
            };
        }
    }

    // 初始化定时任务
    initializeSchedule() {
        if (this.cronJob) {
            this.cronJob.stop();
        }

        let cronExpression;
        switch (this.config.backupFrequency) {
            case 'hourly':
                cronExpression = '0 * * * *'; // 每小时
                break;
            case 'daily':
                cronExpression = '0 2 * * *'; // 每天凌晨2点
                break;
            case 'weekly':
                cronExpression = '0 2 * * 0'; // 每周日凌晨2点
                break;
            case 'monthly':
                cronExpression = '0 2 1 * *'; // 每月1号凌晨2点
                break;
            default:
                cronExpression = '0 2 * * *'; // 默认每天
        }

        this.cronJob = cron.schedule(cronExpression, () => {
            this.performAutoBackup();
        }, {
            scheduled: false
        });

        this.cronJob.start();
        console.log(`💾 备份定时任务已启动: ${this.config.backupFrequency} (${cronExpression})`);
    }

    // 确保备份目录存在
    ensureBackupDirectory() {
        if (!fs.existsSync(this.config.backupPath)) {
            fs.mkdirSync(this.config.backupPath, { recursive: true });
            console.log('💾 创建备份目录:', this.config.backupPath);
        }
    }

    // 获取需要备份的文件列表
    getBackupFiles() {
        const files = [];
        const dataFiles = [
            'users.json',
            'upc_requests.json',
            'upc_pool.json',
            'system_settings.json',
            'upc_recycle_records.json'
        ];

        for (const file of dataFiles) {
            if (fs.existsSync(file)) {
                files.push(file);
            }
        }

        return files;
    }

    // 执行备份
    async performBackup(backupName = null) {
        return new Promise((resolve, reject) => {
            try {
                this.ensureBackupDirectory();

                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                const backupFileName = backupName || `backup_${timestamp}.zip`;
                const backupFilePath = path.join(this.config.backupPath, backupFileName);

                const output = fs.createWriteStream(backupFilePath);
                const archive = archiver('zip', {
                    zlib: { level: 9 } // 最高压缩级别
                });

                output.on('close', () => {
                    const sizeInKB = (archive.pointer() / 1024).toFixed(2);
                    console.log(`💾 备份完成: ${backupFileName} (${sizeInKB} KB)`);
                    resolve({
                        success: true,
                        fileName: backupFileName,
                        filePath: backupFilePath,
                        size: archive.pointer(),
                        timestamp: new Date()
                    });
                });

                archive.on('error', (err) => {
                    console.error('💾 备份失败:', err.message);
                    reject(err);
                });

                archive.pipe(output);

                // 添加数据文件到备份
                const files = this.getBackupFiles();
                for (const file of files) {
                    archive.file(file, { name: file });
                }

                // 添加备份信息文件
                const backupInfo = {
                    timestamp: new Date().toISOString(),
                    files: files,
                    version: '1.0',
                    description: '自动备份'
                };
                archive.append(JSON.stringify(backupInfo, null, 2), { name: 'backup_info.json' });

                archive.finalize();

            } catch (error) {
                console.error('💾 备份过程出错:', error.message);
                reject(error);
            }
        });
    }

    // 自动备份
    async performAutoBackup() {
        try {
            console.log('💾 开始执行自动备份...');
            const result = await this.performBackup();
            
            // 清理过期备份
            await this.cleanupOldBackups();
            
            return result;
        } catch (error) {
            console.error('💾 自动备份失败:', error.message);
            throw error;
        }
    }

    // 清理过期备份
    async cleanupOldBackups() {
        try {
            if (!fs.existsSync(this.config.backupPath)) {
                return;
            }

            const files = fs.readdirSync(this.config.backupPath);
            const backupFiles = files.filter(file => file.startsWith('backup_') && file.endsWith('.zip'));

            if (backupFiles.length <= this.config.backupRetention) {
                return;
            }

            // 按修改时间排序
            const fileStats = backupFiles.map(file => {
                const filePath = path.join(this.config.backupPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    path: filePath,
                    mtime: stats.mtime
                };
            });

            fileStats.sort((a, b) => b.mtime - a.mtime);

            // 删除超过保留数量的文件
            const filesToDelete = fileStats.slice(this.config.backupRetention);
            
            for (const file of filesToDelete) {
                fs.unlinkSync(file.path);
                console.log(`💾 清理过期备份: ${file.name}`);
            }

            if (filesToDelete.length > 0) {
                console.log(`💾 清理完成，删除了 ${filesToDelete.length} 个过期备份文件`);
            }

        } catch (error) {
            console.error('💾 清理过期备份失败:', error.message);
        }
    }

    // 获取备份列表
    getBackupList() {
        try {
            if (!fs.existsSync(this.config.backupPath)) {
                return [];
            }

            const files = fs.readdirSync(this.config.backupPath);
            const backupFiles = files.filter(file => file.startsWith('backup_') && file.endsWith('.zip'));

            const backupList = backupFiles.map(file => {
                const filePath = path.join(this.config.backupPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    path: filePath,
                    size: stats.size,
                    createTime: stats.birthtime,
                    modifyTime: stats.mtime
                };
            });

            // 按修改时间倒序排列
            backupList.sort((a, b) => b.modifyTime - a.modifyTime);

            return backupList;
        } catch (error) {
            console.error('💾 获取备份列表失败:', error.message);
            return [];
        }
    }

    // 恢复备份
    async restoreBackup(backupFileName) {
        // 这里应该实现备份恢复逻辑
        // 由于涉及到数据安全，建议手动操作
        throw new Error('备份恢复功能需要手动操作，请联系系统管理员');
    }

    // 重新加载配置并重启定时任务
    reloadConfig() {
        this.loadConfig();
        this.initializeSchedule();
    }

    // 验证备份文件
    validateBackupFile(backupFileName) {
        try {
            const backupPath = path.join(this.config.backupPath, backupFileName);

            if (!fs.existsSync(backupPath)) {
                throw new Error('备份文件不存在');
            }

            const stats = fs.statSync(backupPath);
            if (stats.size === 0) {
                throw new Error('备份文件为空');
            }

            return {
                valid: true,
                path: backupPath,
                size: stats.size,
                createTime: stats.birthtime
            };
        } catch (error) {
            return {
                valid: false,
                error: error.message
            };
        }
    }

    // 恢复备份（准备阶段）
    async prepareRestore(backupFileName) {
        try {
            const validation = this.validateBackupFile(backupFileName);

            if (!validation.valid) {
                throw new Error(validation.error);
            }

            console.log(`💾 备份文件验证成功: ${backupFileName}`);
            console.log(`📁 文件路径: ${validation.path}`);
            console.log(`📊 文件大小: ${(validation.size / 1024).toFixed(2)}KB`);

            // 这里可以添加更多的预检查
            // 例如：检查备份文件的完整性、版本兼容性等

            return {
                success: true,
                backupFile: backupFileName,
                backupPath: validation.path,
                backupSize: validation.size,
                createTime: validation.createTime,
                message: '备份文件验证成功，可以进行恢复操作'
            };

        } catch (error) {
            console.error('备份恢复准备失败:', error.message);
            throw error;
        }
    }

    // 删除指定备份文件
    async deleteBackup(backupFileName) {
        try {
            const backupPath = path.join(this.config.backupPath, backupFileName);

            if (!fs.existsSync(backupPath)) {
                throw new Error('备份文件不存在');
            }

            const stats = fs.statSync(backupPath);
            const fileSize = stats.size;

            fs.unlinkSync(backupPath);

            console.log(`🗑️ 备份文件已删除: ${backupFileName} (${(fileSize / 1024).toFixed(2)}KB)`);

            return {
                deletedFile: backupFileName,
                fileSize: fileSize,
                deletedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('删除备份文件失败:', error.message);
            throw error;
        }
    }

    // 清理过期备份文件
    async cleanupOldBackups() {
        try {
            const backupList = this.getBackupList();
            const retentionDays = this.config.backupRetention || 30;
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

            const expiredBackups = backupList.filter(backup => {
                return new Date(backup.createTime) < cutoffDate;
            });

            const deletedFiles = [];
            let totalSizeFreed = 0;

            for (const backup of expiredBackups) {
                try {
                    fs.unlinkSync(backup.path);
                    deletedFiles.push({
                        name: backup.name,
                        size: backup.size,
                        createTime: backup.createTime
                    });
                    totalSizeFreed += backup.size;
                    console.log(`🗑️ 清理过期备份: ${backup.name}`);
                } catch (error) {
                    console.error(`清理备份文件失败 ${backup.name}:`, error.message);
                }
            }

            const result = {
                totalDeleted: deletedFiles.length,
                deletedFiles: deletedFiles,
                totalSizeFreed: totalSizeFreed,
                retentionDays: retentionDays,
                cleanupTime: new Date().toISOString()
            };

            if (deletedFiles.length > 0) {
                console.log(`🧹 过期备份清理完成: 删除 ${deletedFiles.length} 个文件，释放 ${(totalSizeFreed / 1024).toFixed(2)}KB 空间`);
            } else {
                console.log('🧹 过期备份清理完成: 没有需要清理的文件');
            }

            return result;

        } catch (error) {
            console.error('清理过期备份失败:', error.message);
            throw error;
        }
    }

    // 停止备份服务
    stop() {
        if (this.cronJob) {
            this.cronJob.stop();
            console.log('💾 备份定时任务已停止');
        }
    }
}

module.exports = BackupService;
