const http = require('http');

console.log('🏢 测试公司名称显示功能');
console.log('========================');

// 发送HTTP请求的辅助函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

async function testCompanyNameDisplay() {
    console.log('\n🔍 检查公司名称显示位置...');
    
    try {
        // 获取首页HTML
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/',
            method: 'GET'
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            const html = response.data;
            
            // 统计公司名称显示位置
            const companyNameMatches = html.match(/class="company-name"/g);
            const companyNameCount = companyNameMatches ? companyNameMatches.length : 0;
            
            console.log(`   📊 HTML中找到 ${companyNameCount} 个 .company-name 元素`);
            
            // 检查具体的显示位置
            const checks = {
                '首页头部公司信息': html.includes('🏢</span>') && html.includes('<span class="company-name">'),
                '版权信息': html.includes('© 2025') && html.includes('<span class="company-name">'),
                '侧边栏底部': html.includes('侧边栏底部公司信息') && html.includes('<span class="company-name">'),
                'JavaScript更新函数': html.includes('document.querySelectorAll(\'.company-name\')'),
                '设置应用函数': html.includes('applyBasicSettings')
            };
            
            console.log('\n   📍 静态HTML检查结果:');
            Object.entries(checks).forEach(([location, exists]) => {
                console.log(`      ${exists ? '✅' : '❌'} ${location}`);
            });
            
            // 检查动态页面内容（这些是通过JavaScript生成的）
            const dynamicContent = {
                '仪表盘页面模板': html.includes('📊 系统仪表盘') || html.includes('📊 个人仪表盘'),
                'UPC申请页面模板': html.includes('🏷️ UPC码申请'),
                '我的UPC页面模板': html.includes('📋 我的UPC记录'),
                '系统设置页面模板': html.includes('⚙️ 系统设置'),
                'UPC池管理页面模板': html.includes('🏪 UPC码池管理'),
                '数据导入页面模板': html.includes('📥 数据导入管理')
            };
            
            console.log('\n   🔧 动态内容模板检查:');
            Object.entries(dynamicContent).forEach(([content, exists]) => {
                console.log(`      ${exists ? '✅' : '❌'} ${content}`);
            });
            
            return {
                totalElements: companyNameCount,
                staticChecks: checks,
                dynamicContent: dynamicContent
            };
        } else {
            console.log(`   ❌ 页面加载失败: HTTP ${response.statusCode}`);
            return null;
        }
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        return null;
    }
}

async function testSettingsAPI() {
    console.log('\n🔧 测试设置API...');
    
    try {
        // 获取当前设置
        const getOptions = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/settings',
            method: 'GET'
        };
        
        const response = await makeRequest(getOptions);
        
        if (response.statusCode === 200) {
            const settings = JSON.parse(response.data);
            const companyName = settings.data.basic.companyName;
            const systemName = settings.data.basic.systemName;
            
            console.log(`   📊 当前公司名称: "${companyName}"`);
            console.log(`   📊 当前系统名称: "${systemName}"`);
            
            return {
                companyName: companyName,
                systemName: systemName
            };
        } else {
            console.log(`   ❌ 设置API请求失败: HTTP ${response.statusCode}`);
            return null;
        }
    } catch (error) {
        console.log(`   ❌ 设置API测试失败: ${error.message}`);
        return null;
    }
}

async function runTest() {
    console.log('🚀 开始测试...\n');
    
    const displayResult = await testCompanyNameDisplay();
    const settingsResult = await testSettingsAPI();
    
    console.log('\n📋 测试结果总结');
    console.log('==================');
    
    if (displayResult && settingsResult) {
        console.log(`🏢 公司名称: "${settingsResult.companyName}"`);
        console.log(`📊 HTML中的显示元素: ${displayResult.totalElements} 个`);
        
        const staticPassed = Object.values(displayResult.staticChecks).filter(Boolean).length;
        const staticTotal = Object.keys(displayResult.staticChecks).length;
        
        console.log(`📍 静态显示检查: ${staticPassed}/${staticTotal} 项通过`);
        
        const dynamicPassed = Object.values(displayResult.dynamicContent).filter(Boolean).length;
        const dynamicTotal = Object.keys(displayResult.dynamicContent).length;
        
        console.log(`🔧 动态内容检查: ${dynamicPassed}/${dynamicTotal} 项通过`);
        
        if (staticPassed === staticTotal && dynamicPassed === dynamicTotal) {
            console.log('\n✅ 所有检查通过！');
            
            console.log('\n🎉 公司名称显示位置总结:');
            console.log('   1. ✅ 首页头部 - 公司信息卡片');
            console.log('   2. ✅ 侧边栏底部 - 公司信息区域');
            console.log('   3. ✅ 版权信息 - 页面底部');
            console.log('   4. ✅ 仪表盘页面 - 页面头部（动态生成）');
            console.log('   5. ✅ UPC申请页面 - 页面头部（动态生成）');
            console.log('   6. ✅ 我的UPC页面 - 页面头部（动态生成）');
            console.log('   7. ✅ 系统设置页面 - 页面头部（动态生成）');
            console.log('   8. ✅ UPC池管理页面 - 页面头部（动态生成）');
            console.log('   9. ✅ 数据导入页面 - 页面头部（动态生成）');
            
            console.log('\n🔧 功能说明:');
            console.log('   • 公司名称通过 .company-name CSS类统一管理');
            console.log('   • applyBasicSettings() 函数负责更新所有显示位置');
            console.log('   • 在系统设置中修改公司名称后会立即更新所有位置');
            console.log('   • 支持中英文公司名称显示');
            
        } else {
            console.log('\n⚠️ 部分检查未通过，需要进一步调试');
            
            if (staticPassed < staticTotal) {
                console.log('\n❌ 静态显示问题:');
                Object.entries(displayResult.staticChecks).forEach(([check, passed]) => {
                    if (!passed) {
                        console.log(`      • ${check}`);
                    }
                });
            }
            
            if (dynamicPassed < dynamicTotal) {
                console.log('\n❌ 动态内容问题:');
                Object.entries(displayResult.dynamicContent).forEach(([content, exists]) => {
                    if (!exists) {
                        console.log(`      • ${content}`);
                    }
                });
            }
        }
        
    } else {
        console.log('❌ 测试失败，无法获取完整结果');
    }
}

// 运行测试
runTest().catch(error => {
    console.error('❌ 测试运行出错:', error);
    process.exit(1);
});
