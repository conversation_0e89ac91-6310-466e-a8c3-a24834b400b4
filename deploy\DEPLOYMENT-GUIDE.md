# UPC管理系统V2.5 部署指南

## 📦 部署包信息

- **系统名称**: UPC管理系统
- **版本**: V2.5 正式版
- **构建日期**: 2025-07-01
- **部署包大小**: 0.18 MB
- **包含文件**: 23个文件

## 🎯 系统特性

- ✅ UPC码管理和分配
- ✅ 用户权限管理
- ✅ 数据备份和恢复
- ✅ 邮件和短信通知
- ✅ 操作日志记录
- ✅ 库存预警系统

## 🔧 系统要求

### 最低要求
- **Node.js**: >= 14.0.0
- **NPM**: >= 6.0.0
- **操作系统**: Windows/Linux/macOS
- **内存**: >= 512MB
- **磁盘空间**: >= 100MB

### 推荐配置
- **Node.js**: >= 16.0.0
- **NPM**: >= 8.0.0
- **内存**: >= 1GB
- **磁盘空间**: >= 500MB

## 🚀 快速部署

### 1. 解压部署包
```bash
# 解压到目标目录
unzip UPC-Management-System-V2.5-Deploy-2025-07-01.zip
cd UPC-Management-System-V2.5
```

### 2. 自动安装（推荐）

**Windows系统:**
```cmd
# 双击运行或命令行执行
install.bat
```

**Linux/macOS系统:**
```bash
# 添加执行权限并运行
chmod +x install.sh
./install.sh
```

### 3. 手动安装

```bash
# 安装依赖
npm install

# 启动系统
npm start
# 或者
node simple-server.js
```

## 🌐 访问系统

- **访问地址**: http://localhost:3001
- **默认端口**: 3001（可在system_settings.json中修改）

## 👤 默认账户

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 管理员 | sutuo_admin | Sutuo@2025! | 全部权限 |
| 业务经理 | manager | Manager@2025 | 业务管理 |
| 操作员 | operator | Operator@2025 | 基础操作 |

## ⚙️ 配置说明

### 端口配置
在 `system_settings.json` 中修改：
```json
{
  "server": {
    "port": 3001
  }
}
```

### 邮件服务配置
```json
{
  "email": {
    "enabled": true,
    "service": "163",
    "user": "<EMAIL>",
    "pass": "your-password"
  }
}
```

### 短信服务配置
```json
{
  "sms": {
    "enabled": true,
    "provider": "tencent",
    "secretId": "your-secret-id",
    "secretKey": "your-secret-key"
  }
}
```

## 📁 目录结构

```
UPC-Management-System-V2.5/
├── simple-server.js          # 主服务器文件
├── package.json              # 项目配置
├── system_settings.json      # 系统配置
├── install.bat               # Windows安装脚本
├── install.sh                # Linux/macOS安装脚本
├── public/
│   └── index.html            # 前端页面
├── data/                     # 数据文件
│   ├── users.json           # 用户数据
│   ├── upc_codes.json       # UPC码数据
│   ├── applications.json    # 申请记录
│   ├── recycle_records.json # 回收记录
│   └── reports.json         # 报告数据
├── logs/                     # 日志目录
├── email-service.js          # 邮件服务
├── sms-service.js           # 短信服务
├── logger-service.js        # 日志服务
├── backup-service.js        # 备份服务
└── 文档/
    ├── README.md            # 项目说明
    ├── DEPLOYMENT.md        # 部署文档
    ├── USER_MANUAL.md       # 用户手册
    └── V2.5-Release-Notes.md # 版本说明
```

## 🔒 安全建议

1. **修改默认密码**: 首次登录后立即修改所有默认账户密码
2. **配置HTTPS**: 生产环境建议配置SSL证书
3. **防火墙设置**: 仅开放必要端口
4. **定期备份**: 启用自动备份功能
5. **日志监控**: 定期检查系统日志

## 🛠️ 常见问题

### Q: 端口被占用怎么办？
A: 修改 `system_settings.json` 中的端口号，或停止占用端口的程序

### Q: 无法发送邮件/短信？
A: 检查 `system_settings.json` 中的邮件和短信配置是否正确

### Q: 数据丢失怎么办？
A: 系统支持自动备份，可从 `backups` 目录恢复数据

### Q: 忘记管理员密码？
A: 可以编辑 `data/users.json` 文件重置密码

## 📞 技术支持

如遇到部署问题，请检查：
1. Node.js版本是否符合要求
2. 网络连接是否正常
3. 端口是否被占用
4. 配置文件格式是否正确

## 🔄 升级说明

从旧版本升级时：
1. 备份当前数据
2. 停止旧版本服务
3. 部署新版本
4. 恢复数据文件
5. 重启服务

## 📋 部署检查清单

- [ ] Node.js环境已安装
- [ ] 部署包已解压
- [ ] 依赖包已安装
- [ ] 配置文件已修改
- [ ] 服务已启动
- [ ] 网页可正常访问
- [ ] 默认账户可登录
- [ ] 邮件/短信功能已测试
- [ ] 备份功能已启用

---

**部署完成后，请访问 http://localhost:3001 开始使用系统！**
