# UPC管理系统 V2.3 生产版

## 📋 项目概述

UPC管理系统是一个现代化的企业级UPC码管理解决方案，提供完整的UPC码分配、使用追踪、回收管理和数据统计功能。

### 🌟 主要特性

- **用户认证系统**: 支持多角色用户管理（管理员、业务经理、操作员）
- **UPC码管理**: 完整的UPC码生命周期管理
- **申请流程**: 规范化的UPC码申请和分配流程
- **回收系统**: 未使用UPC码的回收和重新激活
- **数据统计**: 实时统计和历史数据分析
- **响应式设计**: 支持桌面端和移动端访问

## 🛠️ 技术栈

- **后端**: Node.js + Express
- **数据存储**: JSON文件数据库
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **图表**: Chart.js
- **样式**: 现代CSS Grid/Flexbox布局

## 📦 系统要求

- Node.js >= 14.0.0
- npm >= 6.0.0
- 现代浏览器（Chrome、Firefox、Safari、Edge）

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动服务器

```bash
# 开发模式
npm start

# 生产模式
npm run production
```

### 3. 访问系统

打开浏览器访问: http://localhost:3001

## 👥 默认账户

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 系统管理员 | sutuo_admin | Sutuo@2025! | 全部权限 |
| 业务经理 | manager | Manager@2025 | 用户权限 |
| 操作员 | operator | Operator@2025 | 用户权限 |

## 📁 项目结构

```
UPC-Management-System-V2.3/
├── public/
│   └── index.html          # 前端单页应用
├── data/                   # 数据存储目录
│   ├── users.json         # 用户数据
│   ├── upc_codes.json     # UPC码数据
│   ├── applications.json  # 申请记录
│   ├── recycle_records.json # 回收记录
│   └── reports.json       # 报告数据
├── simple-server.js       # 主服务器文件
├── package.json           # 项目配置
├── system-diagnostic.js   # 系统诊断工具
├── data-cleanup.js        # 数据清理工具
├── frontend-optimizer.js  # 前端优化分析
├── remove-debug-logs.js   # 调试日志清理
├── system-test.js         # 系统测试工具
└── README.md              # 项目文档
```

## 🔧 API接口

### 认证接口
- `POST /api/auth/login` - 用户登录

### UPC管理接口
- `GET /api/stats` - 获取系统统计
- `POST /api/upc/request` - 申请UPC码
- `POST /api/upc/recycle` - 回收UPC码
- `POST /api/upc/delete` - 删除UPC码
- `GET /api/upc/pool-stats` - UPC池统计
- `GET /api/upc/pool-data` - UPC池数据

### 历史记录接口
- `GET /api/user/history` - 用户历史记录
- `GET /api/recycle/history` - 回收历史记录

### 用户管理接口（管理员）
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户

## 🎯 主要功能

### 1. 用户管理
- 多角色权限控制
- 用户信息管理
- 登录状态管理

### 2. UPC码管理
- UPC码导入和生成
- 状态管理（可用、已分配、已回收、无效）
- 批量操作支持

### 3. 申请流程
- 在线申请UPC码
- 申请历史追踪
- 自动分配机制

### 4. 回收系统
- 未使用UPC码回收
- 批量回收操作
- 重新激活功能

### 5. 数据统计
- 实时统计仪表板
- 历史数据分析
- 图表可视化

## 🔍 系统监控

### 健康检查
访问 `/api/health` 获取系统状态

### 诊断工具
```bash
# 运行系统诊断
node system-diagnostic.js

# 数据清理
node data-cleanup.js

# 系统测试
node system-test.js
```

## 🛡️ 安全特性

- 用户认证和授权
- 输入验证和清理
- CORS跨域保护
- 错误处理和日志记录

## 📊 性能优化

- 前端代码优化（已移除调试日志）
- 数据缓存机制
- 响应式设计
- 懒加载支持

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `simple-server.js` 中的 PORT 变量
   - 或终止占用端口的进程

2. **数据文件损坏**
   - 运行 `node data-cleanup.js` 修复数据
   - 检查 `data/` 目录权限

3. **登录失败**
   - 检查用户名和密码
   - 确认用户数据文件完整

### 日志查看
服务器日志会显示在控制台，包含：
- 用户登录记录
- API请求日志
- 错误信息
- 系统状态

## 📈 版本历史

### V2.3 (当前版本)
- ✅ 修复了 `getUPCPoolStats` 函数未定义的bug
- ✅ 清理了21个无效的回收记录
- ✅ 移除了重复的函数定义
- ✅ 删除了282个调试日志，减少17.9KB文件大小
- ✅ 优化了前端代码结构
- ✅ 完善了系统测试覆盖率100%

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 📞 支持

- 公司: 深圳速拓电子商务有限公司
- 邮箱: <EMAIL>
- 电话: 400-888-8888
- 网站: https://sutuo.net

---

**注意**: 这是生产环境版本，请在部署前确保所有配置正确，并定期备份数据文件。
