<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本设置测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-item {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .display-area {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 基本设置自动刷新测试</h1>
        <p>此页面用于测试基本设置保存后的自动刷新功能</p>
        
        <div class="form-item">
            <label>系统名称</label>
            <input type="text" id="systemName" value="UPC管理系统" placeholder="输入系统名称">
        </div>
        
        <div class="form-item">
            <label>公司名称</label>
            <input type="text" id="companyName" value="深圳速拓电子商务有限公司" placeholder="输入公司名称">
        </div>
        
        <div class="form-item">
            <label>时区设置</label>
            <select id="timezone">
                <option value="Asia/Shanghai" selected>中国标准时间 (UTC+8)</option>
                <option value="UTC">协调世界时 (UTC)</option>
                <option value="America/New_York">美国东部时间</option>
            </select>
        </div>
        
        <button onclick="testSaveBasicSettings()">💾 保存并测试自动刷新</button>
        
        <div class="display-area">
            <h3>📊 当前显示效果</h3>
            <p><strong>页面标题:</strong> <span id="currentTitle">-</span></p>
            <p><strong>系统名称:</strong> <span class="system-name">UPC管理系统</span></p>
            <p><strong>公司名称:</strong> <span class="company-name">深圳速拓电子商务有限公司</span></p>
            <p><strong>版权信息:</strong> © 2025 <span class="company-name">深圳速拓电子商务有限公司</span> 版权所有</p>
        </div>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>

    <script>
        // 更新当前页面标题显示
        function updateCurrentTitle() {
            document.getElementById('currentTitle').textContent = document.title;
        }
        
        // 页面加载时更新标题显示
        updateCurrentTitle();
        
        // 应用基本设置到页面
        function applyBasicSettings(settings) {
            console.log('🔄 应用基本设置:', settings);
            
            // 更新页面标题
            if (settings.systemName) {
                document.title = `${settings.systemName} - 测试页面`;
                updateCurrentTitle();
            }
            
            // 更新系统名称显示
            const systemNameElements = document.querySelectorAll('.system-name');
            if (settings.systemName) {
                systemNameElements.forEach(element => {
                    element.textContent = settings.systemName;
                });
            }
            
            // 更新公司名称显示
            const companyElements = document.querySelectorAll('.company-name');
            if (settings.companyName) {
                companyElements.forEach(element => {
                    element.textContent = settings.companyName;
                });
            }
        }
        
        // 测试保存基本设置
        async function testSaveBasicSettings() {
            const systemName = document.getElementById('systemName').value;
            const companyName = document.getElementById('companyName').value;
            const timezone = document.getElementById('timezone').value;
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>⏳ 正在保存设置...</p>';
            
            try {
                // 发送保存请求
                const response = await fetch('http://localhost:3001/api/settings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        category: 'basic',
                        settings: {
                            systemName,
                            companyName,
                            timezone
                        }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = '<p class="success">✅ 基本设置保存成功！</p>';
                    
                    // 立即应用新的设置到界面（模拟自动刷新）
                    const newSettings = {
                        systemName: systemName,
                        companyName: companyName,
                        timezone: timezone
                    };
                    
                    setTimeout(() => {
                        applyBasicSettings(newSettings);
                        resultDiv.innerHTML += '<p class="success">🔄 界面已自动刷新！</p>';
                    }, 500);
                    
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ 保存失败: ${data.message}</p>`;
                }
            } catch (error) {
                console.error('保存失败:', error);
                resultDiv.innerHTML = '<p class="error">❌ 保存失败，请检查网络连接</p>';
            }
        }
        
        // 监听输入变化，实时预览
        document.getElementById('systemName').addEventListener('input', function() {
            const systemNameElements = document.querySelectorAll('.system-name');
            systemNameElements.forEach(element => {
                element.textContent = this.value || 'UPC管理系统';
            });
            document.title = `${this.value || 'UPC管理系统'} - 测试页面`;
            updateCurrentTitle();
        });
        
        document.getElementById('companyName').addEventListener('input', function() {
            const companyElements = document.querySelectorAll('.company-name');
            companyElements.forEach(element => {
                element.textContent = this.value || '深圳速拓电子商务有限公司';
            });
        });
    </script>
</body>
</html>
