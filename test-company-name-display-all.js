const http = require('http');

console.log('🏢 测试公司名称在所有位置的显示');
console.log('================================');

// 辅助函数：发送HTTP请求
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 测试公司名称显示位置
async function testCompanyNameDisplay() {
    console.log('\n🔍 检查公司名称显示位置...');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/',
            method: 'GET'
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            const html = response.data;
            
            // 检查所有公司名称显示位置
            const companyNameMatches = html.match(/class="company-name"/g);
            const companyNameCount = companyNameMatches ? companyNameMatches.length : 0;
            
            console.log(`   📊 找到 ${companyNameCount} 个公司名称显示位置`);
            
            // 检查具体位置
            const locations = {
                '首页头部': html.includes('🏢</span>') && html.includes('class="company-name"'),
                '版权信息': html.includes('© 2025') && html.includes('class="company-name"'),
                '侧边栏底部': html.includes('侧边栏底部公司信息') && html.includes('class="company-name"'),
                '仪表盘页面': html.includes('📊 系统仪表盘') && html.includes('class="company-name"'),
                'UPC申请页面': html.includes('🏷️ UPC码申请') && html.includes('class="company-name"'),
                '我的UPC页面': html.includes('📋 我的UPC记录') && html.includes('class="company-name"'),
                '系统设置页面': html.includes('⚙️ 系统设置') && html.includes('class="company-name"'),
                'UPC池管理页面': html.includes('🏪 UPC码池管理') && html.includes('class="company-name"'),
                '数据导入页面': html.includes('📥 数据导入管理') && html.includes('class="company-name"')
            };
            
            console.log('\n   📍 各位置检查结果:');
            Object.entries(locations).forEach(([location, exists]) => {
                console.log(`      ${exists ? '✅' : '❌'} ${location}`);
            });
            
            // 检查JavaScript更新函数
            const hasUpdateFunction = html.includes('document.querySelectorAll(\'.company-name\')');
            const hasApplyFunction = html.includes('applyBasicSettings');
            
            console.log('\n   🔧 JavaScript功能检查:');
            console.log(`      ${hasUpdateFunction ? '✅' : '❌'} 公司名称更新函数`);
            console.log(`      ${hasApplyFunction ? '✅' : '❌'} 设置应用函数`);
            
            return {
                totalLocations: companyNameCount,
                locations: locations,
                hasUpdateFunction: hasUpdateFunction,
                hasApplyFunction: hasApplyFunction
            };
        } else {
            console.log(`   ❌ 页面加载失败: HTTP ${response.statusCode}`);
            return null;
        }
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        return null;
    }
}

// 测试公司名称动态更新
async function testCompanyNameUpdate() {
    console.log('\n🔄 测试公司名称动态更新...');
    
    try {
        // 先保存一个测试公司名称
        const testCompanyName = '测试科技有限公司';
        
        const settingOptions = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/settings',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        };
        
        const settingData = JSON.stringify({
            category: 'basic',
            settings: {
                systemName: 'UPC管理系统',
                companyName: testCompanyName,
                timezone: 'Asia/Shanghai'
            }
        });
        
        const settingResponse = await makeRequest(settingOptions, settingData);
        
        if (settingResponse.statusCode === 200) {
            const result = JSON.parse(settingResponse.data);
            if (result.success) {
                console.log(`   ✅ 测试公司名称保存成功: "${testCompanyName}"`);
                
                // 验证设置是否正确保存
                const getOptions = {
                    hostname: 'localhost',
                    port: 3001,
                    path: '/api/settings',
                    method: 'GET'
                };
                
                const getResponse = await makeRequest(getOptions);
                if (getResponse.statusCode === 200) {
                    const settings = JSON.parse(getResponse.data);
                    const savedCompanyName = settings.data.basic.companyName;
                    
                    console.log(`   📊 服务器端保存的公司名称: "${savedCompanyName}"`);
                    
                    if (savedCompanyName === testCompanyName) {
                        console.log('   ✅ 公司名称保存验证成功');
                        
                        // 恢复原来的公司名称
                        const restoreData = JSON.stringify({
                            category: 'basic',
                            settings: {
                                systemName: 'UPC管理系统',
                                companyName: '深圳速拓电子商务有限公司',
                                timezone: 'Asia/Shanghai'
                            }
                        });
                        
                        await makeRequest(settingOptions, restoreData);
                        console.log('   🔄 已恢复原始公司名称');
                        
                        return true;
                    } else {
                        console.log('   ❌ 公司名称保存验证失败');
                        return false;
                    }
                } else {
                    console.log('   ❌ 获取设置失败');
                    return false;
                }
            } else {
                console.log(`   ❌ 保存失败: ${result.message}`);
                return false;
            }
        } else {
            console.log(`   ❌ 保存请求失败: HTTP ${settingResponse.statusCode}`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        return false;
    }
}

// 主测试函数
async function runAllTests() {
    console.log('🚀 开始运行公司名称显示测试...\n');
    
    const displayResult = await testCompanyNameDisplay();
    const updateResult = await testCompanyNameUpdate();
    
    console.log('\n📋 测试结果总结');
    console.log('==================');
    
    if (displayResult) {
        console.log(`🏢 公司名称显示位置: ${displayResult.totalLocations} 个`);
        
        const workingLocations = Object.values(displayResult.locations).filter(Boolean).length;
        const totalLocations = Object.keys(displayResult.locations).length;
        
        console.log(`📍 页面位置检查: ${workingLocations}/${totalLocations} 个位置正常`);
        console.log(`🔧 JavaScript功能: ${displayResult.hasUpdateFunction && displayResult.hasApplyFunction ? '正常' : '异常'}`);
        
        if (workingLocations === totalLocations) {
            console.log('✅ 所有页面位置都包含公司名称显示');
        } else {
            console.log('⚠️ 部分页面位置缺少公司名称显示');
            console.log('\n   缺少的位置:');
            Object.entries(displayResult.locations).forEach(([location, exists]) => {
                if (!exists) {
                    console.log(`      ❌ ${location}`);
                }
            });
        }
    } else {
        console.log('❌ 公司名称显示检查失败');
    }
    
    console.log(`🔄 动态更新测试: ${updateResult ? '✅ 通过' : '❌ 失败'}`);
    
    const allPassed = displayResult && 
                     Object.values(displayResult.locations).every(Boolean) && 
                     displayResult.hasUpdateFunction && 
                     displayResult.hasApplyFunction && 
                     updateResult;
    
    console.log('\n🎯 总体结果:');
    if (allPassed) {
        console.log('✅ 所有测试通过！公司名称在所有位置都能正常显示和更新。');
        
        console.log('\n🎉 公司名称显示位置:');
        console.log('   1. ✅ 首页头部 - 公司信息卡片');
        console.log('   2. ✅ 侧边栏底部 - 公司信息区域');
        console.log('   3. ✅ 仪表盘页面 - 页面头部');
        console.log('   4. ✅ UPC申请页面 - 页面头部');
        console.log('   5. ✅ 我的UPC页面 - 页面头部');
        console.log('   6. ✅ 系统设置页面 - 页面头部');
        console.log('   7. ✅ UPC池管理页面 - 页面头部');
        console.log('   8. ✅ 数据导入页面 - 页面头部');
        console.log('   9. ✅ 版权信息 - 页面底部');
        
        console.log('\n🔧 使用说明:');
        console.log('   • 在系统设置 → 基本设置中修改公司名称');
        console.log('   • 保存后所有位置的公司名称会立即更新');
        console.log('   • 支持中英文公司名称显示');
        
    } else {
        console.log('❌ 部分测试失败，需要进一步检查。');
        
        if (!displayResult) {
            console.log('\n⚠️ 显示检查问题: 无法获取页面内容');
        } else if (!Object.values(displayResult.locations).every(Boolean)) {
            console.log('\n⚠️ 显示位置问题: 部分页面缺少公司名称显示');
        }
        
        if (!updateResult) {
            console.log('\n⚠️ 动态更新问题: 公司名称保存或更新功能异常');
        }
    }
    
    return allPassed;
}

// 运行测试
runAllTests().catch(error => {
    console.error('❌ 测试运行出错:', error);
    process.exit(1);
});
