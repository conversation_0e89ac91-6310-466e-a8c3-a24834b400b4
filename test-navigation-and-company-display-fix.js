const http = require('http');
const fs = require('fs');

console.log('🧪 测试导航修复和公司名称显示优化');
console.log('=====================================');

// 检查showHome函数修复
function checkShowHomeFix() {
    console.log('\n📍 检查showHome函数修复...');
    
    try {
        const content = fs.readFileSync('public/index.html', 'utf8');
        
        // 检查showHome函数是否已修复
        const showHomeMatch = content.match(/function showHome\(\)\s*\{([^}]+)\}/);
        
        if (showHomeMatch) {
            const functionBody = showHomeMatch[1].trim();
            
            if (functionBody.includes('location.reload()')) {
                console.log('   ❌ showHome函数仍使用location.reload()');
                return false;
            } else if (functionBody.includes('showDashboard()')) {
                console.log('   ✅ showHome函数已修复为调用showDashboard()');
                return true;
            } else {
                console.log('   ⚠️  showHome函数内容未知:', functionBody);
                return false;
            }
        } else {
            console.log('   ❌ 未找到showHome函数');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 检查showHome函数时出错: ${error.message}`);
        return false;
    }
}

// 检查公司名称显示位置
function checkCompanyNameDisplay() {
    console.log('\n🏢 检查公司名称显示位置...');
    
    try {
        const content = fs.readFileSync('public/index.html', 'utf8');
        
        // 查找所有公司名称显示位置
        const companyNameMatches = [...content.matchAll(/class="company-name"/g)];
        
        console.log(`   📊 总共找到 ${companyNameMatches.length} 个公司名称显示位置`);
        
        // 分析每个位置的上下文
        const positions = [];
        
        companyNameMatches.forEach((match, index) => {
            const position = match.index;
            const contextStart = Math.max(0, position - 200);
            const contextEnd = Math.min(content.length, position + 200);
            const context = content.substring(contextStart, contextEnd);
            
            // 判断位置类型
            let locationType = '未知位置';
            
            if (context.includes('登录页面') || context.includes('专业的UPC码管理解决方案')) {
                locationType = '登录页面';
            } else if (context.includes('仪表盘') || context.includes('系统仪表盘') || context.includes('个人仪表盘')) {
                locationType = '仪表盘页面';
            } else if (context.includes('侧边栏') || context.includes('V2.6 正式版')) {
                locationType = '侧边栏底部';
            } else if (context.includes('版权所有')) {
                locationType = '版权信息';
            } else if (context.includes('UPC申请') || context.includes('申请新的UPC码')) {
                locationType = 'UPC申请页面头部';
            } else if (context.includes('数据导入') || context.includes('批量导入UPC码')) {
                locationType = '数据导入页面头部';
            } else if (context.includes('我的UPC记录') || context.includes('申请和使用历史')) {
                locationType = '我的UPC记录页面头部';
            } else if (context.includes('UPC池管理') || context.includes('UPC码资源')) {
                locationType = 'UPC池管理页面头部';
            } else if (context.includes('系统设置') || context.includes('配置系统参数')) {
                locationType = '系统设置页面头部';
            }
            
            positions.push({
                index: index + 1,
                type: locationType,
                context: context.substring(100, 300).replace(/\s+/g, ' ').trim()
            });
        });
        
        // 统计位置类型
        const locationCounts = {};
        positions.forEach(pos => {
            locationCounts[pos.type] = (locationCounts[pos.type] || 0) + 1;
        });
        
        console.log('\n   📋 公司名称显示位置统计:');
        Object.entries(locationCounts).forEach(([type, count]) => {
            const shouldKeep = ['登录页面', '仪表盘页面', '侧边栏底部', '版权信息'].includes(type);
            const status = shouldKeep ? '✅ 保留' : '❌ 应移除';
            console.log(`      ${status} ${type}: ${count}个`);
        });
        
        // 检查是否还有不应该存在的位置
        const unwantedTypes = ['UPC申请页面头部', '数据导入页面头部', '我的UPC记录页面头部', 'UPC池管理页面头部', '系统设置页面头部'];
        const hasUnwanted = unwantedTypes.some(type => locationCounts[type] > 0);
        
        if (hasUnwanted) {
            console.log('\n   ⚠️  仍有不应该显示公司名称的位置:');
            unwantedTypes.forEach(type => {
                if (locationCounts[type] > 0) {
                    console.log(`      - ${type}: ${locationCounts[type]}个`);
                }
            });
            return false;
        } else {
            console.log('\n   ✅ 公司名称显示位置已正确优化');
            return true;
        }
        
    } catch (error) {
        console.log(`   ❌ 检查公司名称显示时出错: ${error.message}`);
        return false;
    }
}

// 检查页面结构完整性
function checkPageStructure() {
    console.log('\n🏗️  检查页面结构完整性...');
    
    try {
        const content = fs.readFileSync('public/index.html', 'utf8');
        
        const checks = [
            { name: 'showDashboard函数', pattern: /function showDashboard\(/, found: false },
            { name: 'showUPCAllocation函数', pattern: /function showUPCAllocation\(\)/, found: false },
            { name: 'showMyUPCHistory函数', pattern: /function showMyUPCHistory\(\)/, found: false },
            { name: 'showUPCPoolManagement函数', pattern: /function showUPCPoolManagement\(\)/, found: false },
            { name: 'showSystemSettings函数', pattern: /function showSystemSettings\(\)/, found: false },
            { name: 'showDataImport函数', pattern: /function showDataImport\(\)/, found: false }
        ];
        
        checks.forEach(check => {
            check.found = check.pattern.test(content);
            const status = check.found ? '✅' : '❌';
            console.log(`   ${status} ${check.name}`);
        });
        
        return checks.every(check => check.found);
    } catch (error) {
        console.log(`   ❌ 检查页面结构时出错: ${error.message}`);
        return false;
    }
}

// 生成修复报告
function generateFixReport(results) {
    console.log('\n📋 生成修复报告...');
    
    const report = `# 导航修复和公司名称显示优化报告

## 修复时间
${new Date().toLocaleString('zh-CN')}

## 修复内容

### 1. 导航问题修复
**问题**: showHome()函数使用location.reload()导致页面刷新返回仪表盘
**修复**: 将showHome()函数改为调用showDashboard()
**状态**: ${results.navigation ? '✅ 已修复' : '❌ 修复失败'}

### 2. 公司名称显示优化
**问题**: 各模块页面头部都显示公司名称，界面冗余
**修复**: 移除各模块页面头部的公司名称，只保留以下位置：
- ✅ 登录页面
- ✅ 仪表盘页面
- ✅ 侧边栏底部
- ✅ 版权信息

**状态**: ${results.companyDisplay ? '✅ 已优化' : '❌ 优化失败'}

### 3. 页面结构完整性
**检查**: 确保所有页面函数正常存在
**状态**: ${results.structure ? '✅ 完整' : '❌ 有问题'}

## 修复效果

### 导航体验改进
- 🔄 点击"返回首页"不再刷新整个页面
- ⚡ 页面切换更加流畅
- 🎯 保持当前页面状态和数据

### 界面简化效果
- 🎨 各模块页面头部更加简洁
- 👁️ 减少视觉干扰
- 🏢 公司名称在关键位置仍然显示

## 技术细节

### showHome函数修复
\`\`\`javascript
// 修复前
function showHome() {
    location.reload();  // 会刷新整个页面
}

// 修复后
function showHome() {
    showDashboard();    // 直接跳转到仪表盘
}
\`\`\`

### 公司名称显示位置
保留位置：
- 登录页面：展示企业形象
- 仪表盘：主要工作页面
- 侧边栏底部：版权标识
- 版权信息：法律要求

移除位置：
- UPC申请页面头部
- 数据导入页面头部
- 我的UPC记录页面头部
- UPC池管理页面头部
- 系统设置页面头部

## 测试建议

1. **导航测试**
   - 在各个页面点击"返回首页"按钮
   - 验证是否直接跳转到仪表盘而不刷新页面
   - 检查页面状态是否保持

2. **界面测试**
   - 浏览各个功能模块
   - 确认页面头部不再显示公司名称
   - 验证侧边栏和仪表盘仍显示公司名称

3. **功能测试**
   - 确保所有页面功能正常
   - 验证页面切换流畅性
   - 检查用户体验改进效果

## 总体评估
${Object.values(results).every(r => r) ? '🎉 所有修复均已完成，系统优化成功！' : '⚠️ 部分修复需要进一步处理'}

---
*修复报告由自动化测试脚本生成*
`;

    try {
        fs.writeFileSync('Navigation-Company-Display-Fix-Report.md', report, 'utf8');
        console.log('   ✅ 修复报告已生成: Navigation-Company-Display-Fix-Report.md');
        return true;
    } catch (error) {
        console.log(`   ❌ 生成修复报告失败: ${error.message}`);
        return false;
    }
}

// 主测试函数
async function runFixTest() {
    console.log('🚀 开始测试修复效果...\n');
    
    const results = {
        navigation: checkShowHomeFix(),
        companyDisplay: checkCompanyNameDisplay(),
        structure: checkPageStructure()
    };
    
    // 生成修复报告
    await generateFixReport(results);
    
    console.log('\n🎯 修复测试结果总结');
    console.log('===================');
    
    const allFixed = Object.values(results).every(r => r);
    
    if (allFixed) {
        console.log('🎉 所有问题均已修复！');
        
        console.log('\n✅ 修复内容:');
        console.log('   🔄 导航问题: showHome函数不再刷新页面');
        console.log('   🏢 公司名称显示: 已优化为仅在关键位置显示');
        console.log('   🏗️  页面结构: 所有功能函数完整');
        
        console.log('\n🎯 用户体验改进:');
        console.log('   ⚡ 页面切换更加流畅');
        console.log('   🎨 界面更加简洁清爽');
        console.log('   🔄 保持页面状态和数据');
        
        console.log('\n📋 建议测试:');
        console.log('   1. 在各页面点击"返回首页"测试导航');
        console.log('   2. 检查各模块页面头部是否简化');
        console.log('   3. 确认关键位置仍显示公司名称');
        
    } else {
        console.log('❌ 部分问题仍需修复:');
        
        Object.entries(results).forEach(([key, fixed]) => {
            if (!fixed) {
                const problemNames = {
                    navigation: '导航问题',
                    companyDisplay: '公司名称显示',
                    structure: '页面结构'
                };
                console.log(`   ❌ ${problemNames[key]}修复失败`);
            }
        });
    }
    
    return allFixed;
}

// 运行测试
runFixTest().catch(error => {
    console.error('❌ 测试运行出错:', error);
    process.exit(1);
});
