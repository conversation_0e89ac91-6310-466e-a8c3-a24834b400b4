@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    UPC管理系统 V2.6.0 正式版 安装程序
echo ========================================
echo.

:: 检查Node.js是否已安装
echo [1/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js环境
    echo 请先安装Node.js 14.0或更高版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm是否可用
echo.
echo [2/6] 检查npm包管理器...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm不可用
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

:: 安装依赖包
echo.
echo [3/6] 安装项目依赖...
echo 正在安装必要的npm包，请稍候...
npm install
if %errorlevel% neq 0 (
    echo ❌ 错误: 依赖包安装失败
    echo 请检查网络连接或尝试使用国内镜像:
    echo npm config set registry https://registry.npmmirror.com/
    pause
    exit /b 1
)
echo ✅ 依赖包安装完成

:: 创建数据目录
echo.
echo [4/6] 初始化数据目录...
if not exist "data" mkdir data
if not exist "data\backups" mkdir data\backups
echo ✅ 数据目录创建完成

:: 创建默认配置文件
echo.
echo [5/6] 创建默认配置...
if not exist "data\users.json" (
    echo [{"id":"admin","username":"admin","password":"admin123","role":"admin","email":"<EMAIL>","phone":"","createdAt":"2025-07-01T00:00:00.000Z","lastLogin":""}] > data\users.json
    echo ✅ 默认用户配置已创建
)

if not exist "data\system_settings.json" (
    echo {"basic":{"systemName":"UPC管理系统","companyName":"深圳速拓电子商务有限公司","timezone":"Asia/Shanghai"},"upc":{"defaultPrefix":"","maxAllocation":100},"email":{"enabled":false,"host":"","port":587,"secure":false,"user":"","pass":""},"sms":{"enabled":false,"provider":"","apiKey":"","apiSecret":"","signName":""},"backup":{"enabled":true,"interval":"daily","maxFiles":30}} > data\system_settings.json
    echo ✅ 系统设置配置已创建
)

:: 测试服务器启动
echo.
echo [6/6] 测试系统启动...
echo 正在测试服务器启动，请稍候...
timeout /t 2 /nobreak >nul

start /b node simple-server.js
timeout /t 3 /nobreak >nul

:: 检查服务是否启动成功
curl -s http://localhost:3001 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 系统启动测试成功
    
    :: 停止测试服务
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
        taskkill /pid %%a /f >nul 2>&1
    )
    
    echo.
    echo ========================================
    echo           🎉 安装完成！
    echo ========================================
    echo.
    echo 📋 系统信息:
    echo    版本: UPC管理系统 V2.6.0 正式版
    echo    端口: 3001
    echo    访问地址: http://localhost:3001
    echo.
    echo 👤 默认管理员账户:
    echo    用户名: admin
    echo    密码: admin123
    echo.
    echo 🚀 启动系统:
    echo    方法1: 双击 start.bat
    echo    方法2: 运行命令 node simple-server.js
    echo.
    echo 📚 更多信息请查看 docs 目录中的文档
    echo.
    
) else (
    echo ❌ 系统启动测试失败
    echo 请检查端口3001是否被占用
    echo 或手动运行: node simple-server.js
)

echo 按任意键退出...
pause >nul
