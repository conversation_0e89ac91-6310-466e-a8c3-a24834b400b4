@echo off
chcp 65001 >nul
title UPC管理系统 V2.6.0 正式版

echo.
echo ========================================
echo    UPC管理系统 V2.6.0 正式版 启动中...
echo ========================================
echo.

:: 检查Node.js环境
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js环境
    echo 请先运行 scripts\install.bat 进行安装
    pause
    exit /b 1
)

:: 检查依赖是否安装
if not exist "node_modules" (
    echo ❌ 错误: 依赖包未安装
    echo 请先运行 scripts\install.bat 进行安装
    pause
    exit /b 1
)

:: 检查端口是否被占用
netstat -ano | findstr :3001 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  警告: 端口3001已被占用
    echo 请关闭占用端口的程序或修改配置文件中的端口号
    pause
)

echo 🚀 正在启动UPC管理系统...
echo 📍 访问地址: http://localhost:3001
echo 👤 管理员账户: admin / admin123
echo.
echo 💡 提示: 按 Ctrl+C 可停止服务器
echo ========================================
echo.

:: 启动服务器
node simple-server.js

echo.
echo 服务器已停止运行
pause
