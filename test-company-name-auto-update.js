const http = require('http');

console.log('🔄 测试公司名称自动更新功能');
console.log('================================');

// 发送HTTP请求的辅助函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

async function getCurrentSettings() {
    console.log('\n📊 获取当前设置...');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/settings',
            method: 'GET'
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            const settings = JSON.parse(response.data);
            const companyName = settings.data.basic.companyName;
            const systemName = settings.data.basic.systemName;
            
            console.log(`   🏢 当前公司名称: "${companyName}"`);
            console.log(`   📊 当前系统名称: "${systemName}"`);
            
            return {
                companyName: companyName,
                systemName: systemName
            };
        } else {
            console.log(`   ❌ 获取设置失败: HTTP ${response.statusCode}`);
            return null;
        }
    } catch (error) {
        console.log(`   ❌ 获取设置失败: ${error.message}`);
        return null;
    }
}

async function updateCompanyName(newCompanyName) {
    console.log(`\n🔧 更新公司名称为: "${newCompanyName}"`);
    
    try {
        const postData = JSON.stringify({
            category: 'basic',
            settings: {
                systemName: 'UPC管理系统',
                companyName: newCompanyName,
                timezone: 'Asia/Shanghai'
            }
        });

        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/settings',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        const response = await makeRequest(options, postData);
        
        if (response.statusCode === 200) {
            const result = JSON.parse(response.data);
            if (result.success) {
                console.log('   ✅ 公司名称更新成功');
                return true;
            } else {
                console.log(`   ❌ 更新失败: ${result.message}`);
                return false;
            }
        } else {
            console.log(`   ❌ 更新失败: HTTP ${response.statusCode}`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 更新失败: ${error.message}`);
        return false;
    }
}

async function checkPageCompanyName(pageName) {
    console.log(`\n🔍 检查 ${pageName} 页面的公司名称显示...`);
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/',
            method: 'GET'
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            const html = response.data;
            
            // 检查是否包含公司名称显示元素
            const hasCompanyNameElements = html.includes('class="company-name"');
            const hasUpdateFunction = html.includes('loadAndApplyBasicSettings');
            const hasApplyFunction = html.includes('applyBasicSettings');
            
            console.log(`   📍 包含公司名称元素: ${hasCompanyNameElements ? '✅' : '❌'}`);
            console.log(`   🔄 包含加载应用函数: ${hasUpdateFunction ? '✅' : '❌'}`);
            console.log(`   ⚙️ 包含应用设置函数: ${hasApplyFunction ? '✅' : '❌'}`);
            
            return {
                hasElements: hasCompanyNameElements,
                hasUpdateFunction: hasUpdateFunction,
                hasApplyFunction: hasApplyFunction
            };
        } else {
            console.log(`   ❌ 页面加载失败: HTTP ${response.statusCode}`);
            return null;
        }
    } catch (error) {
        console.log(`   ❌ 检查失败: ${error.message}`);
        return null;
    }
}

async function testAutoUpdateFunctionality() {
    console.log('\n🧪 测试自动更新功能...');
    
    // 1. 获取当前设置
    const currentSettings = await getCurrentSettings();
    if (!currentSettings) {
        console.log('❌ 无法获取当前设置，测试终止');
        return false;
    }
    
    const originalCompanyName = currentSettings.companyName;
    
    // 2. 检查页面结构
    const pageCheck = await checkPageCompanyName('主页面');
    if (!pageCheck || !pageCheck.hasElements || !pageCheck.hasUpdateFunction || !pageCheck.hasApplyFunction) {
        console.log('❌ 页面结构检查失败，自动更新功能可能不完整');
        return false;
    }
    
    // 3. 测试更新功能
    const testCompanyName = '测试公司名称 - ' + new Date().getTime();
    const updateSuccess = await updateCompanyName(testCompanyName);
    
    if (!updateSuccess) {
        console.log('❌ 公司名称更新失败');
        return false;
    }
    
    // 4. 等待一段时间让前端应用设置
    console.log('   ⏳ 等待前端应用设置...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 5. 验证设置是否已保存
    const updatedSettings = await getCurrentSettings();
    if (!updatedSettings || updatedSettings.companyName !== testCompanyName) {
        console.log('❌ 设置保存验证失败');
        // 恢复原始设置
        await updateCompanyName(originalCompanyName);
        return false;
    }
    
    console.log('   ✅ 设置保存验证成功');
    
    // 6. 恢复原始设置
    console.log('   🔄 恢复原始设置...');
    const restoreSuccess = await updateCompanyName(originalCompanyName);
    
    if (!restoreSuccess) {
        console.log('⚠️ 恢复原始设置失败，请手动恢复');
        return false;
    }
    
    console.log('   ✅ 原始设置已恢复');
    
    return true;
}

async function runTest() {
    console.log('🚀 开始测试...\n');
    
    const testResult = await testAutoUpdateFunctionality();
    
    console.log('\n📋 测试结果总结');
    console.log('==================');
    
    if (testResult) {
        console.log('✅ 公司名称自动更新功能测试通过！');
        
        console.log('\n🎉 修复内容总结:');
        console.log('   1. ✅ 仪表盘页面 - 添加了 loadAndApplyBasicSettings() 调用');
        console.log('   2. ✅ UPC申请页面 - 添加了 loadAndApplyBasicSettings() 调用');
        console.log('   3. ✅ 我的UPC页面 - 添加了 loadAndApplyBasicSettings() 调用');
        console.log('   4. ✅ 系统设置页面 - 添加了 loadAndApplyBasicSettings() 调用');
        console.log('   5. ✅ UPC池管理页面 - 添加了 loadAndApplyBasicSettings() 调用');
        console.log('   6. ✅ 数据导入页面 - 添加了 loadAndApplyBasicSettings() 调用');
        
        console.log('\n🔧 工作原理:');
        console.log('   • 每个页面渲染后都会调用 loadAndApplyBasicSettings()');
        console.log('   • loadAndApplyBasicSettings() 从API获取最新设置');
        console.log('   • applyBasicSettings() 更新所有 .company-name 元素');
        console.log('   • 设置保存后立即应用到当前页面');
        console.log('   • 切换页面时自动加载最新设置');
        
        console.log('\n📝 使用说明:');
        console.log('   1. 在系统设置中修改公司名称');
        console.log('   2. 点击保存后，当前页面立即更新');
        console.log('   3. 切换到其他页面时，公司名称也会正确显示');
        console.log('   4. 所有页面的公司名称都会保持同步');
        
    } else {
        console.log('❌ 公司名称自动更新功能测试失败');
        
        console.log('\n🔧 可能的问题:');
        console.log('   • API接口不可用');
        console.log('   • 页面结构不完整');
        console.log('   • JavaScript函数调用失败');
        console.log('   • 设置保存失败');
        
        console.log('\n💡 建议:');
        console.log('   1. 检查服务器是否正常运行');
        console.log('   2. 检查浏览器控制台是否有错误');
        console.log('   3. 手动测试设置保存功能');
        console.log('   4. 检查页面切换时的网络请求');
    }
}

// 运行测试
runTest().catch(error => {
    console.error('❌ 测试运行出错:', error);
    process.exit(1);
});
