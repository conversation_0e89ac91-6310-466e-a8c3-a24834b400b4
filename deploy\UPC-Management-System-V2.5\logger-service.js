// 日志服务模块
const fs = require('fs');
const path = require('path');

class LoggerService {
    constructor() {
        this.config = null;
        this.logLevels = {
            'error': 0,
            'warn': 1,
            'info': 2,
            'debug': 3
        };
        this.loadConfig();
        this.ensureLogDirectory();
    }

    // 加载日志配置
    loadConfig() {
        try {
            const settingsData = fs.readFileSync('system_settings.json', 'utf8');
            const settings = JSON.parse(settingsData);
            
            if (settings.log || settings.logging) {
                const logSettings = settings.log || settings.logging;
                this.config = {
                    logLevel: logSettings.logLevel || 'info',
                    logRetention: parseInt(logSettings.logRetention) || 30,
                    maxLogSize: parseInt(logSettings.maxLogSize) || 100, // MB
                    enableOperationLog: logSettings.enableOperationLog !== false,
                    logPath: './logs'
                };
            } else {
                // 默认配置
                this.config = {
                    logLevel: 'info',
                    logRetention: 30,
                    maxLogSize: 100,
                    enableOperationLog: true,
                    logPath: './logs'
                };
            }
            
            console.log('📋 日志服务配置加载成功');
        } catch (error) {
            console.log('📋 日志配置加载失败，使用默认配置:', error.message);
            this.config = {
                logLevel: 'info',
                logRetention: 30,
                maxLogSize: 100,
                enableOperationLog: true,
                logPath: './logs'
            };
        }
    }

    // 确保日志目录存在
    ensureLogDirectory() {
        if (!fs.existsSync(this.config.logPath)) {
            fs.mkdirSync(this.config.logPath, { recursive: true });
            console.log('📋 创建日志目录:', this.config.logPath);
        }
    }

    // 检查是否应该记录该级别的日志
    shouldLog(level) {
        const currentLevel = this.logLevels[this.config.logLevel] || 2;
        const messageLevel = this.logLevels[level] || 2;
        return messageLevel <= currentLevel;
    }

    // 格式化日志消息
    formatLogMessage(level, message, category = 'SYSTEM') {
        const timestamp = new Date().toISOString();
        const levelStr = level.toUpperCase().padEnd(5);
        const categoryStr = category.padEnd(10);
        return `[${timestamp}] [${levelStr}] [${categoryStr}] ${message}`;
    }

    // 获取日志文件路径
    getLogFilePath(category = 'system') {
        const date = new Date().toISOString().split('T')[0];
        return path.join(this.config.logPath, `${category}_${date}.log`);
    }

    // 写入日志文件
    writeToFile(filePath, message) {
        try {
            // 检查文件大小
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                const sizeInMB = stats.size / (1024 * 1024);
                
                if (sizeInMB > this.config.maxLogSize) {
                    // 文件过大，创建新文件
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                    const newPath = filePath.replace('.log', `_${timestamp}.log`);
                    fs.renameSync(filePath, newPath);
                    console.log(`📋 日志文件轮转: ${path.basename(newPath)}`);
                }
            }
            
            fs.appendFileSync(filePath, message + '\n');
        } catch (error) {
            console.error('📋 写入日志文件失败:', error.message);
        }
    }

    // 通用日志记录方法
    log(level, message, category = 'SYSTEM') {
        if (!this.shouldLog(level)) {
            return;
        }

        const formattedMessage = this.formatLogMessage(level, message, category);
        
        // 输出到控制台
        switch (level) {
            case 'error':
                console.error(formattedMessage);
                break;
            case 'warn':
                console.warn(formattedMessage);
                break;
            case 'debug':
                if (this.config.logLevel === 'debug') {
                    console.log(formattedMessage);
                }
                break;
            default:
                console.log(formattedMessage);
        }

        // 写入文件
        const logFile = this.getLogFilePath(category.toLowerCase());
        this.writeToFile(logFile, formattedMessage);
    }

    // 错误日志
    error(message, category = 'SYSTEM') {
        this.log('error', message, category);
    }

    // 警告日志
    warn(message, category = 'SYSTEM') {
        this.log('warn', message, category);
    }

    // 信息日志
    info(message, category = 'SYSTEM') {
        this.log('info', message, category);
    }

    // 调试日志
    debug(message, category = 'SYSTEM') {
        this.log('debug', message, category);
    }

    // 操作日志
    operation(action, details, username = 'system') {
        if (!this.config.enableOperationLog) {
            return;
        }

        const message = `用户: ${username} | 操作: ${action} | 详情: ${details}`;
        this.log('info', message, 'OPERATION');
    }

    // 登录日志
    loginLog(username, success, ip, details = '') {
        const status = success ? '成功' : '失败';
        const message = `登录${status} | 用户: ${username} | IP: ${ip} | ${details}`;
        this.log(success ? 'info' : 'warn', message, 'LOGIN');
    }

    // UPC操作日志
    upcLog(action, upcCode, username, details = '') {
        const message = `UPC操作: ${action} | 码: ${upcCode} | 用户: ${username} | ${details}`;
        this.log('info', message, 'UPC');
    }

    // 系统日志
    systemLog(event, details = '') {
        const message = `系统事件: ${event} | ${details}`;
        this.log('info', message, 'SYSTEM');
    }

    // 清理过期日志
    cleanupOldLogs() {
        try {
            if (!fs.existsSync(this.config.logPath)) {
                return;
            }

            const files = fs.readdirSync(this.config.logPath);
            const logFiles = files.filter(file => file.endsWith('.log'));
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - this.config.logRetention);

            let deletedCount = 0;

            for (const file of logFiles) {
                const filePath = path.join(this.config.logPath, file);
                const stats = fs.statSync(filePath);
                
                if (stats.mtime < cutoffDate) {
                    fs.unlinkSync(filePath);
                    deletedCount++;
                    console.log(`📋 清理过期日志: ${file}`);
                }
            }

            if (deletedCount > 0) {
                console.log(`📋 日志清理完成，删除了 ${deletedCount} 个过期日志文件`);
            }

        } catch (error) {
            console.error('📋 清理过期日志失败:', error.message);
        }
    }

    // 获取日志文件列表
    getLogFiles() {
        try {
            if (!fs.existsSync(this.config.logPath)) {
                return [];
            }

            const files = fs.readdirSync(this.config.logPath);
            const logFiles = files.filter(file => file.endsWith('.log'));

            const logList = logFiles.map(file => {
                const filePath = path.join(this.config.logPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    path: filePath,
                    size: stats.size,
                    createTime: stats.birthtime,
                    modifyTime: stats.mtime
                };
            });

            // 按修改时间倒序排列
            logList.sort((a, b) => b.modifyTime - a.modifyTime);

            return logList;
        } catch (error) {
            console.error('📋 获取日志文件列表失败:', error.message);
            return [];
        }
    }

    // 读取日志文件内容
    readLogFile(fileName, lines = 100) {
        try {
            const filePath = path.join(this.config.logPath, fileName);
            
            if (!fs.existsSync(filePath)) {
                throw new Error('日志文件不存在');
            }

            const content = fs.readFileSync(filePath, 'utf8');
            const allLines = content.split('\n').filter(line => line.trim());
            
            // 返回最后N行
            const startIndex = Math.max(0, allLines.length - lines);
            return allLines.slice(startIndex);

        } catch (error) {
            console.error('📋 读取日志文件失败:', error.message);
            throw error;
        }
    }

    // 重新加载配置
    reloadConfig() {
        this.loadConfig();
        this.ensureLogDirectory();
    }
}

// 创建全局日志实例
const logger = new LoggerService();

// 定期清理过期日志（每天执行一次）
setInterval(() => {
    logger.cleanupOldLogs();
}, 24 * 60 * 60 * 1000);

module.exports = logger;
