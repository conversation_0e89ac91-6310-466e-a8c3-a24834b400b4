# UPC管理系统 V2.3 生产版部署指南

## 📋 系统信息
- **版本**: V2.3 生产版
- **构建时间**: 2025/6/30 06:19:54
- **环境**: 生产环境
- **数据库**: 文件数据库

## 🚀 快速部署

### 1. 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- 操作系统: Windows/Linux/macOS

### 2. 安装步骤
```bash
# 1. 解压部署包
# 2. 进入目录
cd UPC-Management-System-V2.3-Production-*

# 3. 安装依赖
npm install --production

# 4. 启动系统
npm start
```

### 3. 生产环境启动
```bash
# 生产模式启动
npm run production

# 或者直接使用
NODE_ENV=production node simple-server.js
```

## 🔧 配置说明

### 环境变量
- `NODE_ENV`: 运行环境 (production/development)
- `PORT`: 服务端口 (默认: 3001)

### 默认账户
- **管理员**: sutuo_admin / Sutuo@2025!
- **业务经理**: manager / Manager@2025
- **操作员**: operator / Operator@2025

## 📁 目录结构
```
UPC-Management-System-V2.3/
├── simple-server.js     # 主服务器文件
├── package.json         # 项目配置
├── public/             # 前端文件
│   └── index.html      # 主页面
├── data/               # 数据文件
│   ├── users.json      # 用户数据
│   ├── upc_codes.json  # UPC码数据
│   └── applications.json # 申请记录
└── docs/               # 文档
```

## 🔒 安全建议
1. 修改默认管理员密码
2. 配置防火墙规则
3. 定期备份数据文件
4. 监控系统日志

## 📞 技术支持
- 公司: 深圳速拓电子商务有限公司
- 网站: https://sutuo.net
- 版本: V2.3 生产版
