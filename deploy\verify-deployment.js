const fs = require('fs');
const path = require('path');

console.log('🔍 UPC管理系统V2.5部署包验证工具');
console.log('=====================================');

// 必需的文件列表
const requiredFiles = [
    'simple-server.js',
    'package.json',
    'package-lock.json',
    'system_settings.json',
    'public/index.html',
    'install.bat',
    'install.sh',
    'email-service.js',
    'sms-service.js',
    'logger-service.js',
    'backup-service.js',
    'start.bat',
    'start.sh',
    'README.md',
    'DEPLOYMENT.md',
    'USER_MANUAL.md',
    'V2.5-Release-Notes.md',
    'deploy-info.json'
];

// 必需的目录列表
const requiredDirectories = [
    'data',
    'logs',
    'public'
];

// 数据文件列表
const dataFiles = [
    'data/users.json',
    'data/upc_codes.json',
    'data/applications.json',
    'data/recycle_records.json',
    'data/reports.json'
];

function verifyDeployment(deployPath) {
    let errors = [];
    let warnings = [];
    let success = [];
    
    console.log('📁 验证路径:', deployPath);
    console.log();
    
    // 检查部署路径是否存在
    if (!fs.existsSync(deployPath)) {
        errors.push('部署路径不存在: ' + deployPath);
        return { errors, warnings, success };
    }
    
    // 验证必需文件
    console.log('📋 检查必需文件...');
    for (const file of requiredFiles) {
        const filePath = path.join(deployPath, file);
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            success.push(`✅ ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
        } else {
            errors.push(`❌ 缺少文件: ${file}`);
        }
    }
    
    // 验证必需目录
    console.log('📂 检查必需目录...');
    for (const dir of requiredDirectories) {
        const dirPath = path.join(deployPath, dir);
        if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
            success.push(`✅ 目录: ${dir}`);
        } else {
            errors.push(`❌ 缺少目录: ${dir}`);
        }
    }
    
    // 验证数据文件
    console.log('💾 检查数据文件...');
    for (const file of dataFiles) {
        const filePath = path.join(deployPath, file);
        if (fs.existsSync(filePath)) {
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                JSON.parse(content); // 验证JSON格式
                success.push(`✅ ${file} (JSON格式正确)`);
            } catch (error) {
                errors.push(`❌ ${file} JSON格式错误: ${error.message}`);
            }
        } else {
            warnings.push(`⚠️ 数据文件不存在: ${file}`);
        }
    }
    
    // 验证配置文件
    console.log('⚙️ 检查配置文件...');
    const configPath = path.join(deployPath, 'system_settings.json');
    if (fs.existsSync(configPath)) {
        try {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            
            // 检查关键配置项
            if (config.server && config.server.port) {
                success.push(`✅ 服务器端口配置: ${config.server.port}`);
            } else {
                warnings.push('⚠️ 缺少服务器端口配置');
            }
            
            if (config.email) {
                success.push('✅ 邮件服务配置存在');
            } else {
                warnings.push('⚠️ 缺少邮件服务配置');
            }
            
            if (config.sms) {
                success.push('✅ 短信服务配置存在');
            } else {
                warnings.push('⚠️ 缺少短信服务配置');
            }
            
        } catch (error) {
            errors.push(`❌ 系统配置文件格式错误: ${error.message}`);
        }
    }
    
    // 验证package.json
    const packagePath = path.join(deployPath, 'package.json');
    if (fs.existsSync(packagePath)) {
        try {
            const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            if (pkg.name) {
                success.push(`✅ 项目名称: ${pkg.name}`);
            }
            
            if (pkg.version) {
                success.push(`✅ 项目版本: ${pkg.version}`);
            }
            
            if (pkg.dependencies) {
                const depCount = Object.keys(pkg.dependencies).length;
                success.push(`✅ 依赖包数量: ${depCount}`);
            }
            
            if (pkg.scripts && pkg.scripts.start) {
                success.push(`✅ 启动脚本: ${pkg.scripts.start}`);
            } else {
                warnings.push('⚠️ 缺少启动脚本配置');
            }
            
        } catch (error) {
            errors.push(`❌ package.json格式错误: ${error.message}`);
        }
    }
    
    // 验证安装脚本
    console.log('🛠️ 检查安装脚本...');
    const installBat = path.join(deployPath, 'install.bat');
    const installSh = path.join(deployPath, 'install.sh');
    
    if (fs.existsSync(installBat)) {
        success.push('✅ Windows安装脚本存在');
    } else {
        warnings.push('⚠️ 缺少Windows安装脚本');
    }
    
    if (fs.existsSync(installSh)) {
        success.push('✅ Unix安装脚本存在');
    } else {
        warnings.push('⚠️ 缺少Unix安装脚本');
    }
    
    return { errors, warnings, success };
}

// 主验证函数
function main() {
    const deployPath = process.argv[2] || path.join(__dirname, 'UPC-Management-System-V2.5');
    
    const result = verifyDeployment(deployPath);
    
    console.log();
    console.log('📊 验证结果');
    console.log('=============');
    
    if (result.success.length > 0) {
        console.log('✅ 成功项目:');
        result.success.forEach(item => console.log('  ' + item));
        console.log();
    }
    
    if (result.warnings.length > 0) {
        console.log('⚠️ 警告项目:');
        result.warnings.forEach(item => console.log('  ' + item));
        console.log();
    }
    
    if (result.errors.length > 0) {
        console.log('❌ 错误项目:');
        result.errors.forEach(item => console.log('  ' + item));
        console.log();
    }
    
    // 总结
    console.log('📈 验证统计:');
    console.log(`  成功: ${result.success.length} 项`);
    console.log(`  警告: ${result.warnings.length} 项`);
    console.log(`  错误: ${result.errors.length} 项`);
    console.log();
    
    if (result.errors.length === 0) {
        console.log('🎉 部署包验证通过！可以进行部署。');
        return 0;
    } else {
        console.log('💥 部署包验证失败！请修复错误后重新验证。');
        return 1;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const exitCode = main();
    process.exit(exitCode);
}

module.exports = { verifyDeployment };
