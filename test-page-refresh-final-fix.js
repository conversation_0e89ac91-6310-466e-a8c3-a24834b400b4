// 页面刷新问题最终修复测试
const fs = require('fs');
const path = require('path');

console.log('🧪 开始测试页面刷新修复...\n');

// 读取HTML文件
const htmlPath = path.join(__dirname, 'public', 'index.html');
const htmlContent = fs.readFileSync(htmlPath, 'utf8');

// 测试1: 检查window.load事件处理器是否包含页面显示逻辑
function testPageDisplayLogic() {
    console.log('📋 测试1: 检查页面显示逻辑');
    
    const windowLoadMatch = htmlContent.match(/window\.addEventListener\('load'[\s\S]*?}\);/);
    if (!windowLoadMatch) {
        console.log('❌ 未找到window.load事件处理器');
        return false;
    }
    
    const loadHandler = windowLoadMatch[0];
    
    // 检查是否包含页面刷新保持逻辑
    const hasRefreshLogic = loadHandler.includes('页面刷新，保持当前页面');
    const hasSwitchStatement = loadHandler.includes('switch(currentPage)');
    const hasShowDashboard = loadHandler.includes('showDashboard(');
    const hasShowUPCAllocation = loadHandler.includes('showUPCAllocation()');
    
    console.log('  ✅ 页面刷新保持逻辑:', hasRefreshLogic ? '存在' : '❌ 缺失');
    console.log('  ✅ 页面切换逻辑:', hasSwitchStatement ? '存在' : '❌ 缺失');
    console.log('  ✅ 仪表盘显示:', hasShowDashboard ? '存在' : '❌ 缺失');
    console.log('  ✅ UPC申请显示:', hasShowUPCAllocation ? '存在' : '❌ 缺失');
    
    return hasRefreshLogic && hasSwitchStatement && hasShowDashboard && hasShowUPCAllocation;
}

// 测试2: 检查DOMContentLoaded是否不包含页面显示逻辑
function testDOMContentLoadedClean() {
    console.log('\n📋 测试2: 检查DOMContentLoaded清洁性');
    
    const domLoadMatch = htmlContent.match(/document\.addEventListener\('DOMContentLoaded'[\s\S]*?}\);/);
    if (!domLoadMatch) {
        console.log('❌ 未找到DOMContentLoaded事件处理器');
        return false;
    }
    
    const domHandler = domLoadMatch[0];
    
    // 检查是否不包含页面显示函数调用
    const hasShowDashboard = domHandler.includes('showDashboard(');
    const hasShowLogin = domHandler.includes('showLogin(');
    const hasShowWelcome = domHandler.includes('showWelcomePage(');
    
    console.log('  ✅ 不包含showDashboard:', !hasShowDashboard ? '正确' : '❌ 仍然存在');
    console.log('  ✅ 不包含showLogin:', !hasShowLogin ? '正确' : '❌ 仍然存在');
    console.log('  ✅ 不包含showWelcome:', !hasShowWelcome ? '正确' : '❌ 仍然存在');
    
    return !hasShowDashboard && !hasShowLogin && !hasShowWelcome;
}

// 测试3: 检查localStorage操作安全性
function testLocalStorageSafety() {
    console.log('\n📋 测试3: 检查localStorage操作安全性');
    
    // 查找所有localStorage.removeItem调用
    const removeItemMatches = htmlContent.match(/localStorage\.removeItem\([^)]+\)/g) || [];
    const clearMatches = htmlContent.match(/localStorage\.clear\(\)/g) || [];
    
    console.log('  📊 localStorage.removeItem调用次数:', removeItemMatches.length);
    console.log('  📊 localStorage.clear调用次数:', clearMatches.length);
    
    // 检查是否有意外的用户数据清除
    const userDataRemoval = removeItemMatches.filter(match => 
        match.includes('upc_user_data') && 
        !match.includes('catch') && 
        !match.includes('logout')
    );
    
    console.log('  ⚠️ 可能的意外用户数据清除:', userDataRemoval.length);
    if (userDataRemoval.length > 0) {
        console.log('    详情:', userDataRemoval);
    }
    
    return userDataRemoval.length === 0;
}

// 测试4: 检查页面状态保持函数
function testPageStatePreservation() {
    console.log('\n📋 测试4: 检查页面状态保持函数');
    
    const hasUpdateActiveMenu = htmlContent.includes('updateActiveMenuFromPage(');
    const hasLoadBasicSettings = htmlContent.includes('loadAndApplyBasicSettings(');
    const hasReplaceState = htmlContent.includes('replaceState(');
    
    console.log('  ✅ updateActiveMenuFromPage函数:', hasUpdateActiveMenu ? '存在' : '❌ 缺失');
    console.log('  ✅ loadAndApplyBasicSettings函数:', hasLoadBasicSettings ? '存在' : '❌ 缺失');
    console.log('  ✅ replaceState函数:', hasReplaceState ? '存在' : '❌ 缺失');
    
    return hasUpdateActiveMenu && hasLoadBasicSettings && hasReplaceState;
}

// 测试5: 检查调试日志
function testDebugLogging() {
    console.log('\n📋 测试5: 检查调试日志');
    
    const debugLogs = [
        '页面加载事件触发',
        '从URL获取页面',
        '检查localStorage中的用户数据',
        '用户数据解析成功',
        '页面刷新，保持当前页面',
        '显示页面内容'
    ];
    
    let logCount = 0;
    debugLogs.forEach(log => {
        if (htmlContent.includes(log)) {
            console.log(`  ✅ ${log}: 存在`);
            logCount++;
        } else {
            console.log(`  ❌ ${log}: 缺失`);
        }
    });
    
    console.log(`  📊 调试日志覆盖率: ${logCount}/${debugLogs.length}`);
    return logCount >= debugLogs.length * 0.8; // 80%覆盖率
}

// 运行所有测试
function runAllTests() {
    const tests = [
        { name: '页面显示逻辑', test: testPageDisplayLogic },
        { name: 'DOMContentLoaded清洁性', test: testDOMContentLoadedClean },
        { name: 'localStorage安全性', test: testLocalStorageSafety },
        { name: '页面状态保持', test: testPageStatePreservation },
        { name: '调试日志', test: testDebugLogging }
    ];
    
    let passedTests = 0;
    
    tests.forEach(({ name, test }) => {
        const result = test();
        if (result) {
            passedTests++;
        }
    });
    
    console.log('\n📊 测试总结:');
    console.log(`✅ 通过测试: ${passedTests}/${tests.length}`);
    console.log(`📈 成功率: ${Math.round(passedTests / tests.length * 100)}%`);
    
    if (passedTests === tests.length) {
        console.log('\n🎉 所有测试通过！页面刷新修复应该已经生效。');
        console.log('\n🔧 下一步操作:');
        console.log('1. 在浏览器中访问 http://localhost:3001');
        console.log('2. 登录系统 (sutuo_admin / Sutuo@2025!)');
        console.log('3. 进入任意功能页面');
        console.log('4. 按F5刷新页面');
        console.log('5. 检查是否仍然保持在当前页面且保持登录状态');
    } else {
        console.log('\n⚠️ 部分测试失败，可能需要进一步修复。');
    }
    
    return passedTests === tests.length;
}

// 执行测试
runAllTests();
