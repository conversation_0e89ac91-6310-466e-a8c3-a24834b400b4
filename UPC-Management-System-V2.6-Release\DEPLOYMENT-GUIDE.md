# UPC管理系统 V2.6.0 正式版 部署指南

## 🎯 部署概述

本指南将帮助您快速部署UPC管理系统V2.6.0正式版。V2.6版本新增了公司名称全局显示和自动更新功能，提供更好的企业级用户体验。

## 📋 部署前准备

### 系统要求
- **操作系统**: Windows 10/11, Windows Server 2016+, Linux, macOS
- **Node.js**: 14.0 或更高版本
- **内存**: 最少 512MB 可用内存
- **存储**: 最少 100MB 可用空间
- **网络**: 可选（用于邮件/短信功能）

### 环境检查
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查端口占用（Windows）
netstat -ano | findstr :3001

# 检查端口占用（Linux/macOS）
lsof -i :3001
```

## 🚀 快速部署

### 方法一：自动安装（推荐）

1. **解压部署包**
   ```
   解压 UPC-Management-System-V2.6-Release.zip
   ```

2. **运行安装脚本**
   ```bash
   # Windows
   cd UPC-Management-System-V2.6-Release
   scripts\install.bat
   
   # Linux/macOS
   cd UPC-Management-System-V2.6-Release
   chmod +x scripts/install.sh
   ./scripts/install.sh
   ```

3. **启动系统**
   ```bash
   # Windows
   start.bat
   
   # Linux/macOS
   ./start.sh
   ```

4. **访问系统**
   - 打开浏览器访问: http://localhost:3001
   - 使用默认账户登录: admin / admin123

### 方法二：手动部署

1. **解压并进入目录**
   ```bash
   cd UPC-Management-System-V2.6-Release
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **创建数据目录**
   ```bash
   mkdir -p data/backups
   ```

4. **启动服务**
   ```bash
   node simple-server.js
   ```

## ⚙️ 配置说明

### 端口配置
默认端口为3001，如需修改请编辑 `simple-server.js`:
```javascript
const PORT = 3001; // 修改为其他端口
```

### 数据目录
- **用户数据**: `data/users.json`
- **系统设置**: `data/system_settings.json`
- **UPC数据**: `data/upc_codes.json`
- **备份文件**: `data/backups/`

### 默认配置
系统将自动创建以下默认配置：
- 管理员账户: admin / admin123
- 系统名称: UPC管理系统
- 公司名称: 深圳速拓电子商务有限公司

## 🔧 高级配置

### 反向代理配置（Nginx）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 系统服务配置（Linux）
创建 `/etc/systemd/system/upc-system.service`:
```ini
[Unit]
Description=UPC Management System V2.6
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/UPC-Management-System-V2.6-Release
ExecStart=/usr/bin/node simple-server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务:
```bash
sudo systemctl enable upc-system
sudo systemctl start upc-system
```

### Windows服务配置
使用 `node-windows` 创建Windows服务:
```bash
npm install -g node-windows
npm link node-windows
```

## 🛡️ 安全配置

### 基础安全
1. **修改默认密码**
   - 首次登录后立即修改管理员密码
   - 设置强密码策略

2. **网络安全**
   - 配置防火墙规则
   - 使用HTTPS（生产环境）
   - 限制访问IP范围

3. **数据安全**
   - 定期备份数据
   - 设置文件权限
   - 监控系统日志

### 防火墙配置
```bash
# Windows防火墙
netsh advfirewall firewall add rule name="UPC System" dir=in action=allow protocol=TCP localport=3001

# Linux iptables
iptables -A INPUT -p tcp --dport 3001 -j ACCEPT

# Linux ufw
ufw allow 3001
```

## 📊 监控和维护

### 系统监控
- **进程监控**: 确保Node.js进程正常运行
- **内存监控**: 监控内存使用情况
- **磁盘监控**: 监控数据目录空间
- **网络监控**: 监控端口连接状态

### 日志管理
```bash
# 查看系统日志
tail -f /var/log/upc-system.log

# Windows事件日志
eventvwr.msc
```

### 备份策略
1. **自动备份**: 在系统设置中启用自动备份
2. **手动备份**: 定期运行备份脚本
3. **异地备份**: 将备份文件复制到其他位置
4. **备份验证**: 定期验证备份文件完整性

## 🔄 升级指南

### 从V2.5升级到V2.6
1. **备份当前数据**
   ```bash
   scripts\backup.bat
   ```

2. **停止当前服务**
   ```bash
   # 停止服务器进程
   ```

3. **替换程序文件**
   - 保留 `data/` 目录
   - 替换其他所有文件

4. **重启服务**
   ```bash
   start.bat
   ```

5. **验证升级**
   - 检查版本号显示为V2.6
   - 验证公司名称显示功能
   - 测试设置自动更新功能

## 🆘 故障排除

### 常见问题

#### 1. 端口占用
**现象**: 启动时提示端口被占用
**解决**:
```bash
# 查找占用进程
netstat -ano | findstr :3001
# 结束进程
taskkill /pid <PID> /f
```

#### 2. 依赖安装失败
**现象**: npm install 失败
**解决**:
```bash
# 清理缓存
npm cache clean --force
# 使用国内镜像
npm config set registry https://registry.npmmirror.com/
# 重新安装
npm install
```

#### 3. 数据文件损坏
**现象**: 系统无法启动或数据丢失
**解决**:
```bash
# 从备份恢复
copy data\backups\users_YYYYMMDD_HHMMSS.json data\users.json
copy data\backups\system_settings_YYYYMMDD_HHMMSS.json data\system_settings.json
```

#### 4. V2.6新功能不工作
**现象**: 公司名称不显示或不自动更新
**解决**:
1. 清除浏览器缓存
2. 检查系统设置中的公司名称配置
3. 查看浏览器控制台错误信息
4. 重启系统服务

### 性能优化
1. **内存优化**: 定期重启服务释放内存
2. **数据优化**: 清理过期数据和日志
3. **缓存优化**: 配置适当的缓存策略
4. **并发优化**: 根据负载调整并发设置

## 📞 技术支持

### 支持渠道
- **文档**: 查看用户手册和FAQ
- **日志**: 检查系统日志和错误信息
- **社区**: 参与技术讨论和经验分享

### 问题报告
报告问题时请提供：
1. 系统版本信息
2. 错误现象描述
3. 操作步骤重现
4. 系统日志信息
5. 环境配置信息

## 📈 性能基准

### 推荐配置
- **开发环境**: 2核CPU, 4GB内存
- **生产环境**: 4核CPU, 8GB内存
- **高负载环境**: 8核CPU, 16GB内存

### 性能指标
- **响应时间**: < 200ms（正常负载）
- **并发用户**: 100+（推荐配置）
- **数据处理**: 10000+ UPC码
- **备份时间**: < 30秒（标准数据量）

---

## 🎉 部署完成

恭喜！您已成功部署UPC管理系统V2.6.0正式版。

### 下一步操作
1. 访问系统并修改默认密码
2. 配置公司信息和系统设置
3. 创建用户账户
4. 导入初始UPC数据
5. 配置备份策略

### V2.6新功能体验
- 在系统设置中配置公司名称
- 观察所有页面的公司名称显示
- 体验设置修改后的自动更新效果

---

*UPC管理系统 V2.6.0 正式版部署指南 - 深圳速拓电子商务有限公司*
