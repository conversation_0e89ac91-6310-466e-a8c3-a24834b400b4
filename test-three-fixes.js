const http = require('http');

console.log('🔧 测试三个修复功能');
console.log('===================');

// 辅助函数：发送HTTP请求
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 测试1: 检查系统设置页面是否包含修复的refreshSystemInfo函数
async function testSystemSettingsToastFix() {
    console.log('\n🔧 测试1: 系统设置弹窗修复...');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/',
            method: 'GET'
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            const html = response.data;
            
            // 检查是否包含修复的函数签名
            const hasFixedFunction = html.includes('refreshSystemInfo(showToast = true)');
            const hasConditionalToast = html.includes('if (showToast)');
            const hasSilentCall = html.includes('refreshSystemInfo(false)');
            
            console.log(`   ✅ 页面加载成功`);
            console.log(`   📊 函数参数修复: ${hasFixedFunction ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 条件提示修复: ${hasConditionalToast ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 静默调用修复: ${hasSilentCall ? '✅ 是' : '❌ 否'}`);
            
            return hasFixedFunction && hasConditionalToast && hasSilentCall;
        } else {
            console.log(`   ❌ 页面加载失败: HTTP ${response.statusCode}`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        return false;
    }
}

// 测试2: 检查基本设置自动刷新修复
async function testBasicSettingsAutoRefresh() {
    console.log('\n🔄 测试2: 基本设置自动刷新修复...');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/',
            method: 'GET'
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            const html = response.data;
            
            // 检查是否包含自动应用设置的代码
            const hasApplySettings = html.includes('applyBasicSettings(newSettings)');
            const hasConsoleLog = html.includes('基本设置已应用到界面');
            const hasNewSettingsObject = html.includes('const newSettings = {');
            
            console.log(`   ✅ 页面加载成功`);
            console.log(`   📊 应用设置调用: ${hasApplySettings ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 控制台日志: ${hasConsoleLog ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 设置对象创建: ${hasNewSettingsObject ? '✅ 是' : '❌ 否'}`);
            
            return hasApplySettings && hasConsoleLog && hasNewSettingsObject;
        } else {
            console.log(`   ❌ 页面加载失败: HTTP ${response.statusCode}`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        return false;
    }
}

// 测试3: 检查数据导入功能和权限控制
async function testDataImportFunction() {
    console.log('\n📥 测试3: 数据导入功能修复...');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/',
            method: 'GET'
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            const html = response.data;
            
            // 检查是否包含数据导入相关功能
            const hasShowDataImport = html.includes('function showDataImport()');
            const hasDataImportRecommendation = html.includes('id="dataImportRecommendation"');
            const hasPermissionCheck = html.includes('currentUser.role === \'admin\'');
            const hasImportAPI = html.includes('/api/upc/import');
            const hasFileImport = html.includes('handleFileImport');
            const hasManualImport = html.includes('handleManualImport');
            
            console.log(`   ✅ 页面加载成功`);
            console.log(`   📊 数据导入函数: ${hasShowDataImport ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 推荐区域ID: ${hasDataImportRecommendation ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 权限检查: ${hasPermissionCheck ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 导入API: ${hasImportAPI ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 文件导入: ${hasFileImport ? '✅ 是' : '❌ 否'}`);
            console.log(`   📊 手动导入: ${hasManualImport ? '✅ 是' : '❌ 否'}`);
            
            return hasShowDataImport && hasDataImportRecommendation && hasPermissionCheck && 
                   hasImportAPI && hasFileImport && hasManualImport;
        } else {
            console.log(`   ❌ 页面加载失败: HTTP ${response.statusCode}`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        return false;
    }
}

// 测试4: 检查服务器端导入API是否存在
async function testImportAPI() {
    console.log('\n🔌 测试4: 服务器端导入API...');
    
    try {
        // 测试导入API（应该返回400因为没有数据，但不应该404）
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/upc/import',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        };
        
        const response = await makeRequest(options, JSON.stringify({}));
        
        // API存在但数据格式错误应该返回400
        const apiExists = response.statusCode === 400;
        
        console.log(`   📊 API状态码: ${response.statusCode}`);
        console.log(`   📊 API存在: ${apiExists ? '✅ 是' : '❌ 否'}`);
        
        if (response.statusCode === 400) {
            try {
                const result = JSON.parse(response.data);
                console.log(`   📊 错误消息: "${result.message}"`);
                return result.message.includes('UPC码数据格式错误');
            } catch (e) {
                return false;
            }
        }
        
        return false;
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        return false;
    }
}

// 主测试函数
async function runAllTests() {
    console.log('🚀 开始运行所有测试...\n');
    
    const results = {
        systemSettingsToast: false,
        basicSettingsRefresh: false,
        dataImportFunction: false,
        importAPI: false
    };
    
    // 运行所有测试
    results.systemSettingsToast = await testSystemSettingsToastFix();
    results.basicSettingsRefresh = await testBasicSettingsAutoRefresh();
    results.dataImportFunction = await testDataImportFunction();
    results.importAPI = await testImportAPI();
    
    // 输出测试结果
    console.log('\n📋 测试结果总结');
    console.log('==================');
    console.log(`🔧 系统设置弹窗修复: ${results.systemSettingsToast ? '✅ 通过' : '❌ 失败'}`);
    console.log(`🔄 基本设置自动刷新: ${results.basicSettingsRefresh ? '✅ 通过' : '❌ 失败'}`);
    console.log(`📥 数据导入功能: ${results.dataImportFunction ? '✅ 通过' : '❌ 失败'}`);
    console.log(`🔌 导入API接口: ${results.importAPI ? '✅ 通过' : '❌ 失败'}`);
    
    const allPassed = Object.values(results).every(result => result);
    
    console.log('\n🎯 总体结果:');
    if (allPassed) {
        console.log('✅ 所有测试通过！三个问题已成功修复。');
        
        console.log('\n🎉 修复效果:');
        console.log('   1. ✅ 进入系统设置不再弹出"系统信息已刷新"提示');
        console.log('   2. ✅ 基本设置保存后会自动刷新系统名称和公司名称');
        console.log('   3. ✅ 数据导入功能已添加，仅管理员可见');
        console.log('   4. ✅ 服务器端导入API已就绪');
        
        console.log('\n🔧 使用说明:');
        console.log('   • 管理员登录后，在UPC码申请页面可看到"数据导入管理"推荐');
        console.log('   • 员工登录时不会看到数据导入推荐');
        console.log('   • 点击"立即导入数据"可进入数据导入管理页面');
        console.log('   • 支持文件导入和手动输入两种方式');
        
    } else {
        console.log('❌ 部分测试失败，需要进一步检查。');
        
        if (!results.systemSettingsToast) {
            console.log('\n⚠️ 系统设置弹窗修复问题:');
            console.log('   - 检查refreshSystemInfo函数参数是否正确添加');
            console.log('   - 检查条件提示逻辑是否正确实现');
        }
        
        if (!results.basicSettingsRefresh) {
            console.log('\n⚠️ 基本设置自动刷新问题:');
            console.log('   - 检查saveBasicSettings函数是否包含applyBasicSettings调用');
            console.log('   - 检查设置对象是否正确创建和传递');
        }
        
        if (!results.dataImportFunction) {
            console.log('\n⚠️ 数据导入功能问题:');
            console.log('   - 检查showDataImport函数是否正确添加');
            console.log('   - 检查权限控制逻辑是否正确实现');
        }
        
        if (!results.importAPI) {
            console.log('\n⚠️ 导入API问题:');
            console.log('   - 检查服务器端/api/upc/import接口是否存在');
            console.log('   - 检查API错误处理是否正确');
        }
    }
    
    return allPassed;
}

// 运行测试
runAllTests().catch(error => {
    console.error('❌ 测试运行出错:', error);
    process.exit(1);
});
