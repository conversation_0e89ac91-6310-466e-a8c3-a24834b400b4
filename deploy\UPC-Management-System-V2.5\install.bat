@echo off
echo 正在安装UPC管理系统V2.5...
echo.

echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js，请先安装Node.js 14.0或更高版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 安装依赖包...
npm install

echo.
echo 安装完成！
echo.
echo 启动命令: npm start 或 node simple-server.js
echo 访问地址: http://localhost:3001
echo.
echo 默认管理员账户:
echo 用户名: sutuo_admin
echo 密码: Sutuo@2025!
echo.
pause
