# UPC管理系统 V2.6.0 正式版

## 🎉 版本信息
- **版本**: V2.6.0 正式版
- **发布日期**: 2025年7月1日
- **技术栈**: HTML5 + JavaScript + Node.js + Express
- **兼容性**: Windows/Linux/macOS，现代浏览器

## ✨ V2.6新功能亮点
- **🏢 公司名称全局显示**: 在所有页面显示公司名称
- **🔄 自动更新机制**: 设置修改后所有页面自动同步
- **⚡ 即时生效**: 无需刷新页面即可看到更新
- **🌐 全局一致**: 确保所有显示位置保持同步

## 📋 系统要求

### 基础环境
- **Node.js**: 14.0 或更高版本
- **npm**: 6.0 或更高版本
- **内存**: 最少512MB可用内存
- **存储**: 最少100MB可用空间
- **网络**: 可选（用于邮件/短信功能）

### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🚀 快速安装

### 方法一：自动安装（推荐）
1. **解压部署包**到目标目录
2. **双击运行** `scripts\install.bat`
3. **等待安装完成**
4. **双击运行** `start.bat` 启动系统

### 方法二：手动安装
```bash
# 1. 解压部署包
# 2. 进入系统目录
cd UPC-Management-System-V2.6-Release

# 3. 安装依赖
npm install

# 4. 启动系统
node simple-server.js
```

## 🔧 配置说明

### 默认配置
- **访问地址**: http://localhost:3001
- **管理员账户**: admin / admin123
- **数据存储**: data/ 目录
- **备份位置**: data/backups/

### 端口修改
如需修改端口，请编辑 `simple-server.js` 文件：
```javascript
const PORT = 3001; // 修改为其他端口
```

## 👤 默认账户

### 管理员账户
- **用户名**: admin
- **密码**: admin123
- **权限**: 完整系统管理权限

### 首次登录建议
1. 立即修改管理员密码
2. 配置公司信息和系统设置
3. 根据需要创建员工账户
4. 配置邮件和短信通知（可选）

## 📚 功能概览

### 核心功能
- **🏷️ UPC码管理**: 申请、分配、回收UPC码
- **👥 用户管理**: 多用户支持，角色权限控制
- **📊 数据统计**: 实时统计图表和报表
- **📧 通知系统**: 邮件和短信通知支持
- **💾 数据备份**: 自动和手动数据备份
- **📱 响应式设计**: 支持桌面和移动设备

### V2.6新增功能
- **🏢 公司名称显示**: 所有页面显示公司名称
- **🔄 设置自动同步**: 修改设置后全局自动更新
- **⚡ 即时更新**: 无需刷新页面的实时更新
- **🎨 界面优化**: 更好的视觉一致性

## 🛠️ 管理工具

### 启动和停止
```bash
# 启动系统
start.bat                    # Windows
node simple-server.js        # 通用

# 停止系统
Ctrl + C                     # 在运行窗口中按下
```

### 数据备份
```bash
# 手动备份
scripts\backup.bat           # Windows

# 自动备份
# 系统会根据设置自动备份
```

### 系统维护
- **日志查看**: 控制台输出实时日志
- **数据清理**: 定期清理过期备份文件
- **性能监控**: 系统设置中查看性能指标

## 📁 目录结构
```
UPC-Management-System-V2.6-Release/
├── simple-server.js          # 主服务器文件
├── package.json              # 项目配置文件
├── start.bat                 # 启动脚本
├── README.md                 # 说明文档
├── public/                   # 前端文件
│   └── index.html            # 主页面文件
├── data/                     # 数据存储目录
│   ├── users.json            # 用户数据
│   ├── system_settings.json  # 系统设置
│   ├── upc_codes.json        # UPC码数据
│   └── backups/              # 备份文件
├── scripts/                  # 工具脚本
│   ├── install.bat           # 安装脚本
│   └── backup.bat            # 备份脚本
└── docs/                     # 文档目录
    └── CHANGELOG-V2.6.md     # 版本变更记录
```

## 🔒 安全建议

### 基础安全
1. **修改默认密码**: 首次登录后立即修改
2. **定期备份**: 启用自动备份功能
3. **访问控制**: 仅允许授权用户访问
4. **网络安全**: 在生产环境中配置防火墙

### 数据保护
- 定期备份重要数据
- 监控系统访问日志
- 及时更新系统版本
- 配置安全的邮件和短信设置

## 🆘 故障排除

### 常见问题

#### 1. 无法启动服务器
**问题**: 运行时提示端口被占用
**解决**: 
- 检查端口3001是否被其他程序占用
- 修改配置文件中的端口号
- 重启计算机后重试

#### 2. 页面无法访问
**问题**: 浏览器显示无法连接
**解决**:
- 确认服务器已正常启动
- 检查防火墙设置
- 尝试使用 127.0.0.1:3001 访问

#### 3. 数据丢失
**问题**: 用户或UPC数据丢失
**解决**:
- 检查 data/ 目录中的JSON文件
- 从 data/backups/ 恢复备份文件
- 重新启动服务器

#### 4. 公司名称不显示
**问题**: V2.6新功能公司名称不显示
**解决**:
- 进入系统设置配置公司名称
- 刷新浏览器页面
- 检查浏览器控制台是否有错误

### 获取帮助
如遇到其他问题，请：
1. 查看控制台错误信息
2. 检查系统日志
3. 联系技术支持

## 📞 技术支持

### 联系方式
- **技术支持**: 通过系统管理员
- **问题反馈**: 记录详细错误信息
- **功能建议**: 欢迎提出改进建议

### 更新说明
- 定期检查新版本发布
- 升级前请备份数据
- 遵循升级指南操作

---

## 🎉 感谢使用

感谢您选择UPC管理系统V2.6！

我们致力于提供稳定、高效、易用的UPC码管理解决方案。如果您在使用过程中有任何问题或建议，请及时反馈。

**祝您使用愉快！** 🚀

---

*UPC管理系统 V2.6.0 正式版 - 深圳速拓电子商务有限公司*
