// 短信发送服务模块 - 支持真实发送
const tencentcloud = require('tencentcloud-sdk-nodejs');
const crypto = require('crypto');
const https = require('https');
const fs = require('fs');
const path = require('path');

class SMSService {
    constructor() {
        this.client = null;
        this.config = null;
        this.lastSendTime = new Map(); // 记录每个手机号的最后发送时间
        this.loadConfig();
    }

    // 加载短信配置
    loadConfig() {
        try {
            const settingsPath = path.join(__dirname, 'system_settings.json');
            if (fs.existsSync(settingsPath)) {
                const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
                this.config = settings.notification?.sms;

                if (this.config && this.config.enabled) {
                    this.createClient();
                    console.log('📱 短信服务配置加载成功');
                } else {
                    console.log('📱 短信服务未启用');
                }
            }
        } catch (error) {
            console.error('📱 短信服务配置加载失败:', error.message);
        }
    }

    // 创建腾讯云SMS客户端
    createClient() {
        try {
            if (!this.config) {
                throw new Error('短信配置未加载');
            }

            const provider = this.config.smsProvider || this.config.provider;
            if (provider === '腾讯云' || provider === 'tencent') {
                const SmsClient = tencentcloud.sms.v20210111.Client;

                const clientConfig = {
                    credential: {
                        secretId: this.config.smsAccessKey || this.config.accessKey,
                        secretKey: this.config.smsAccessSecret || this.config.accessSecret,
                    },
                    region: "ap-beijing",
                    profile: {
                        httpProfile: {
                            endpoint: "sms.tencentcloudapi.com",
                        },
                    },
                };

                this.client = new SmsClient(clientConfig);
                console.log(`📱 腾讯云SMS客户端创建成功`);
            } else {
                console.log(`📱 暂不支持的短信服务商: ${provider || 'undefined'}`);
            }
        } catch (error) {
            console.error('📱 短信客户端创建失败:', error.message);
            this.client = null;
        }
    }

    // 腾讯云短信签名
    generateTencentSignature(params, secretKey) {
        const keys = Object.keys(params).sort();
        const queryString = keys.map(key => `${key}=${params[key]}`).join('&');
        const stringToSign = `POST\nsms.tencentcloudapi.com\n/\n${queryString}`;
        
        return crypto
            .createHmac('sha256', secretKey)
            .update(stringToSign)
            .digest('base64');
    }

    // 发送腾讯云短信
    async sendTencentSMS(phoneNumber, templateParams) {
        if (!this.config || !this.config.enabled) {
            throw new Error('短信服务未启用');
        }

        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = Math.floor(Math.random() * 1000000);

        const params = {
            Action: 'SendSms',
            Version: '2021-01-11',
            Region: 'ap-beijing',
            SmsSdkAppId: this.config.appId,
            SignName: this.config.signature,
            TemplateId: this.config.templateId || 'SMS_123456789',
            PhoneNumberSet: [phoneNumber],
            TemplateParamSet: templateParams,
            Timestamp: timestamp,
            Nonce: nonce,
            SecretId: this.config.accessKey
        };

        const signature = this.generateTencentSignature(params, this.config.accessSecret);
        params.Signature = signature;

        return new Promise((resolve, reject) => {
            const postData = JSON.stringify(params);
            
            const options = {
                hostname: 'sms.tencentcloudapi.com',
                port: 443,
                path: '/',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData),
                    'Host': 'sms.tencentcloudapi.com'
                }
            };

            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        if (response.Response.Error) {
                            reject(new Error(response.Response.Error.Message));
                        } else {
                            resolve(response.Response);
                        }
                    } catch (error) {
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.write(postData);
            req.end();
        });
    }

    // 发送阿里云短信
    async sendAliyunSMS(phoneNumber, templateParams) {
        // 阿里云短信实现
        throw new Error('阿里云短信服务暂未实现，请使用腾讯云');
    }

    // 发送华为云短信
    async sendHuaweiSMS(phoneNumber, templateParams) {
        // 华为云短信实现
        throw new Error('华为云短信服务暂未实现，请使用腾讯云');
    }

    // 发送短信
    async sendSMS(phoneNumber, content, templateParams = []) {
        try {
            if (!this.config || !this.config.enabled) {
                throw new Error('短信服务未启用');
            }

            // 检查发送频率限制（30秒内不能重复发送到同一号码）
            const now = Date.now();
            const lastSendTime = this.lastSendTime.get(phoneNumber);
            if (lastSendTime && (now - lastSendTime) < 30000) {
                const waitTime = Math.ceil((30000 - (now - lastSendTime)) / 1000);
                throw new Error(`发送频率过快，请等待${waitTime}秒后再试`);
            }

            if (!this.client) {
                this.createClient();
                if (!this.client) {
                    throw new Error('短信客户端创建失败');
                }
            }

            // 确保手机号格式正确（添加+86前缀）
            const formattedPhone = phoneNumber.startsWith('+86') ? phoneNumber : `+86${phoneNumber}`;

            let result;

            if (this.config.smsProvider === '腾讯云' || this.config.smsProvider === 'tencent') {
                result = await this.sendTencentCloudSMS(formattedPhone, content, templateParams);
            } else {
                throw new Error(`不支持的短信服务商: ${this.config.smsProvider}`);
            }

            console.log(`📱 短信发送成功: ${phoneNumber}`);

            // 记录发送时间
            this.lastSendTime.set(phoneNumber, now);

            return result;

        } catch (error) {
            console.error(`📱 短信发送失败: ${error.message}`);
            return {
                success: false,
                message: `短信发送失败: ${error.message}`,
                error: error.code || 'UNKNOWN_ERROR'
            };
        }
    }

    // 腾讯云短信发送
    async sendTencentCloudSMS(phoneNumber, content, templateParams = []) {
        try {
            // 获取模板ID，确保不为空
            const templateId = this.config.smsTemplateId || this.config.templateId;
            if (!templateId) {
                throw new Error('短信模板ID未配置');
            }

            const params = {
                PhoneNumberSet: [phoneNumber],
                SmsSdkAppId: this.config.smsAppId || this.config.appId,
                SignName: this.config.smsSignature || this.config.signature,
                TemplateId: templateId,
                TemplateParamSet: templateParams.length > 0 ? templateParams : ['50', '17', '今日']
            };

            console.log('📱 腾讯云SMS发送参数:', {
                phone: phoneNumber,
                appId: params.SmsSdkAppId,
                signature: params.SignName,
                templateId: params.TemplateId,
                params: params.TemplateParamSet
            });

            const response = await this.client.SendSms(params);

            if (response.SendStatusSet && response.SendStatusSet[0]) {
                const status = response.SendStatusSet[0];

                if (status.Code === 'Ok') {
                    return {
                        success: true,
                        message: '短信发送成功',
                        messageId: status.SerialNo,
                        recipient: phoneNumber,
                        fee: status.Fee
                    };
                } else {
                    throw new Error(`腾讯云SMS错误: ${status.Code} - ${status.Message}`);
                }
            } else {
                throw new Error('腾讯云SMS响应格式异常');
            }

        } catch (error) {
            throw new Error(`腾讯云SMS发送失败: ${error.message}`);
        }
    }

    // 发送UPC申请通知短信
    async sendUPCApplicationSMS(phoneNumber, userName, quantity, purpose) {
        const templateParams = [
            userName,
            quantity.toString(),
            purpose,
            new Date().toLocaleString('zh-CN')
        ];

        return await this.sendSMS(phoneNumber, templateParams);
    }

    // 发送库存预警短信
    async sendStockAlertSMS(phoneNumbers, currentStock, threshold) {
        // 模板格式：UPC管理系统通知：亲爱的客户，当前UPC库存不足{1}个，还剩余{2}个，请及时补充！{3}
        const shortageAmount = Math.max(0, threshold - currentStock); // 计算不足数量

        const templateParams = [
            shortageAmount.toString(),  // 参数1：库存不足数量
            currentStock.toString(),    // 参数2：剩余数量
            '今日'                      // 参数3：简短时间
        ];

        const phoneList = phoneNumbers.split(',').map(phone => phone.trim()).filter(phone => phone);
        const results = [];

        for (const phone of phoneList) {
            try {
                const result = await this.sendSMS(phone, templateParams);
                results.push({ phone, success: true, result: result.result });
            } catch (error) {
                results.push({ phone, success: false, error: error.message });
            }
        }

        return results;
    }

    // 发送验证码短信
    async sendVerificationCodeSMS(phoneNumber, code) {
        const templateParams = [
            code,
            '5' // 有效期5分钟
        ];

        return await this.sendSMS(phoneNumber, templateParams);
    }

    // 测试短信发送
    async sendTestSMS(testPhone) {
        const templateParams = [
            '测试用户',
            '1',
            '短信服务测试',
            new Date().toLocaleString('zh-CN')
        ];

        return await this.sendSMS(testPhone, templateParams);
    }

    // 发送测试短信
    async sendTestSMS(phoneNumber) {
        // 模板格式：UPC管理系统通知：亲爱的客户，当前UPC库存不足{1}个，还剩余{2}个，请及时补充！{3}
        const templateParams = [
            '50',  // 参数1：库存不足数量
            '17',  // 参数2：剩余数量
            '今日' // 参数3：简短时间
        ];

        return await this.sendSMS(phoneNumber, '', templateParams);
    }

    // 重新加载配置
    reloadConfig() {
        this.loadConfig();
    }

    // 获取配置状态
    getStatus() {
        return {
            enabled: this.config?.enabled || false,
            configured: !!this.client,
            provider: this.config?.smsProvider,
            signature: this.config?.smsSignature,
            appId: this.config?.smsAppId,
            templateId: this.config?.smsTemplateId
        };
    }
}

module.exports = SMSService;
