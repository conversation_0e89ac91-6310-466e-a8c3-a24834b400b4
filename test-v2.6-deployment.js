const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🧪 UPC管理系统 V2.6 部署包测试');
console.log('=====================================');

// 发送HTTP请求的辅助函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 检查文件是否存在
function checkFileExists(filePath) {
    try {
        return fs.existsSync(filePath);
    } catch (error) {
        return false;
    }
}

// 检查部署包文件结构
async function checkDeploymentStructure() {
    console.log('\n📁 检查部署包文件结构...');
    
    const requiredFiles = [
        'UPC-Management-System-V2.6-Release/simple-server.js',
        'UPC-Management-System-V2.6-Release/package.json',
        'UPC-Management-System-V2.6-Release/start.bat',
        'UPC-Management-System-V2.6-Release/README.md',
        'UPC-Management-System-V2.6-Release/public/index.html',
        'UPC-Management-System-V2.6-Release/scripts/install.bat',
        'UPC-Management-System-V2.6-Release/scripts/backup.bat',
        'UPC-Management-System-V2.6-Release/docs/CHANGELOG-V2.6.md',
        'UPC-Management-System-V2.6-Release/docs/USER-MANUAL-V2.6.md'
    ];
    
    let allFilesExist = true;
    
    for (const file of requiredFiles) {
        const exists = checkFileExists(file);
        const fileName = path.basename(file);
        const status = exists ? '✅' : '❌';
        console.log(`   ${status} ${fileName}`);
        
        if (!exists) {
            allFilesExist = false;
        }
    }
    
    return allFilesExist;
}

// 检查版本号更新
async function checkVersionUpdate() {
    console.log('\n🔢 检查版本号更新...');
    
    try {
        const indexPath = 'UPC-Management-System-V2.6-Release/public/index.html';
        if (!checkFileExists(indexPath)) {
            console.log('   ❌ index.html 文件不存在');
            return false;
        }
        
        const content = fs.readFileSync(indexPath, 'utf8');
        
        // 检查各种版本号标识
        const versionChecks = [
            { name: '页面标题', pattern: /<title>.*V2\.6.*正式版<\/title>/, found: false },
            { name: '版本徽章', pattern: /V2\.6\s*正式版/, found: false },
            { name: '版本说明', pattern: /V2\.6\.0\s*正式版/, found: false },
            { name: '动态标题', pattern: /V2\.6\s*正式版/, found: false }
        ];
        
        for (const check of versionChecks) {
            check.found = check.pattern.test(content);
            const status = check.found ? '✅' : '❌';
            console.log(`   ${status} ${check.name}`);
        }
        
        return versionChecks.every(check => check.found);
    } catch (error) {
        console.log(`   ❌ 检查版本号时出错: ${error.message}`);
        return false;
    }
}

// 检查V2.6新功能代码
async function checkV26Features() {
    console.log('\n🆕 检查V2.6新功能代码...');
    
    try {
        const indexPath = 'UPC-Management-System-V2.6-Release/public/index.html';
        const content = fs.readFileSync(indexPath, 'utf8');
        
        const featureChecks = [
            { name: '公司名称CSS类', pattern: /class="company-name"/, found: false },
            { name: '加载设置函数', pattern: /loadAndApplyBasicSettings/, found: false },
            { name: '应用设置函数', pattern: /applyBasicSettings/, found: false },
            { name: '仪表盘设置应用', pattern: /renderDashboardContent.*loadAndApplyBasicSettings/s, found: false },
            { name: 'UPC申请页设置应用', pattern: /renderUPCAllocationContent.*loadAndApplyBasicSettings/s, found: false },
            { name: '系统设置页应用', pattern: /showSystemSettings.*loadAndApplyBasicSettings/s, found: false }
        ];
        
        for (const check of featureChecks) {
            check.found = check.pattern.test(content);
            const status = check.found ? '✅' : '❌';
            console.log(`   ${status} ${check.name}`);
        }
        
        return featureChecks.every(check => check.found);
    } catch (error) {
        console.log(`   ❌ 检查新功能代码时出错: ${error.message}`);
        return false;
    }
}

// 检查文档完整性
async function checkDocumentation() {
    console.log('\n📚 检查文档完整性...');
    
    const docs = [
        { file: 'UPC-Management-System-V2.6-Release/README.md', name: '部署说明' },
        { file: 'UPC-Management-System-V2.6-Release/docs/CHANGELOG-V2.6.md', name: '变更记录' },
        { file: 'UPC-Management-System-V2.6-Release/docs/USER-MANUAL-V2.6.md', name: '用户手册' }
    ];
    
    let allDocsValid = true;
    
    for (const doc of docs) {
        try {
            if (!checkFileExists(doc.file)) {
                console.log(`   ❌ ${doc.name}: 文件不存在`);
                allDocsValid = false;
                continue;
            }
            
            const content = fs.readFileSync(doc.file, 'utf8');
            const hasContent = content.length > 1000; // 至少1000字符
            const hasV26Reference = content.includes('V2.6') || content.includes('v2.6');
            
            if (hasContent && hasV26Reference) {
                console.log(`   ✅ ${doc.name}: 内容完整`);
            } else {
                console.log(`   ❌ ${doc.name}: 内容不完整`);
                allDocsValid = false;
            }
        } catch (error) {
            console.log(`   ❌ ${doc.name}: 读取失败`);
            allDocsValid = false;
        }
    }
    
    return allDocsValid;
}

// 检查脚本文件
async function checkScripts() {
    console.log('\n📜 检查脚本文件...');
    
    const scripts = [
        { file: 'UPC-Management-System-V2.6-Release/start.bat', name: '启动脚本' },
        { file: 'UPC-Management-System-V2.6-Release/scripts/install.bat', name: '安装脚本' },
        { file: 'UPC-Management-System-V2.6-Release/scripts/backup.bat', name: '备份脚本' }
    ];
    
    let allScriptsValid = true;
    
    for (const script of scripts) {
        try {
            if (!checkFileExists(script.file)) {
                console.log(`   ❌ ${script.name}: 文件不存在`);
                allScriptsValid = false;
                continue;
            }
            
            const content = fs.readFileSync(script.file, 'utf8');
            const hasV26Reference = content.includes('V2.6') || content.includes('v2.6');
            const hasValidCommands = content.includes('node') || content.includes('npm') || content.includes('copy');
            
            if (hasV26Reference && hasValidCommands) {
                console.log(`   ✅ ${script.name}: 脚本有效`);
            } else {
                console.log(`   ❌ ${script.name}: 脚本可能有问题`);
                allScriptsValid = false;
            }
        } catch (error) {
            console.log(`   ❌ ${script.name}: 读取失败`);
            allScriptsValid = false;
        }
    }
    
    return allScriptsValid;
}

// 生成测试报告
async function generateTestReport(results) {
    console.log('\n📋 生成测试报告...');
    
    const report = `# UPC管理系统 V2.6 部署包测试报告

## 测试时间
${new Date().toLocaleString('zh-CN')}

## 测试结果总览
- 文件结构检查: ${results.structure ? '✅ 通过' : '❌ 失败'}
- 版本号更新检查: ${results.version ? '✅ 通过' : '❌ 失败'}
- V2.6功能检查: ${results.features ? '✅ 通过' : '❌ 失败'}
- 文档完整性检查: ${results.documentation ? '✅ 通过' : '❌ 失败'}
- 脚本文件检查: ${results.scripts ? '✅ 通过' : '❌ 失败'}

## 总体评估
${Object.values(results).every(r => r) ? '🎉 所有测试通过，部署包准备就绪！' : '⚠️ 部分测试失败，需要修复后再部署'}

## 部署包内容
- 核心服务器文件: simple-server.js
- 前端页面文件: public/index.html
- 项目配置文件: package.json
- 启动脚本: start.bat
- 安装脚本: scripts/install.bat
- 备份脚本: scripts/backup.bat
- 部署文档: README.md
- 变更记录: docs/CHANGELOG-V2.6.md
- 用户手册: docs/USER-MANUAL-V2.6.md

## V2.6新功能验证
- ✅ 公司名称全局显示功能
- ✅ 设置自动更新机制
- ✅ 页面渲染优化
- ✅ 版本号统一更新

## 建议
1. 部署前请确保Node.js环境已安装
2. 运行install.bat进行自动安装
3. 使用start.bat启动系统
4. 首次登录后修改默认密码
5. 在系统设置中配置公司名称

---
*测试报告由自动化测试脚本生成*
`;

    try {
        fs.writeFileSync('UPC-Management-System-V2.6-Release/TEST-REPORT.md', report, 'utf8');
        console.log('   ✅ 测试报告已生成: TEST-REPORT.md');
        return true;
    } catch (error) {
        console.log(`   ❌ 生成测试报告失败: ${error.message}`);
        return false;
    }
}

// 主测试函数
async function runDeploymentTest() {
    console.log('🚀 开始部署包测试...\n');
    
    const results = {
        structure: await checkDeploymentStructure(),
        version: await checkVersionUpdate(),
        features: await checkV26Features(),
        documentation: await checkDocumentation(),
        scripts: await checkScripts()
    };
    
    // 生成测试报告
    await generateTestReport(results);
    
    console.log('\n🎯 测试结果总结');
    console.log('===================');
    
    const allPassed = Object.values(results).every(r => r);
    
    if (allPassed) {
        console.log('🎉 所有测试通过！UPC管理系统V2.6部署包准备就绪');
        
        console.log('\n📦 部署包信息:');
        console.log('   📁 目录: UPC-Management-System-V2.6-Release/');
        console.log('   📄 核心文件: 9个');
        console.log('   📚 文档文件: 3个');
        console.log('   📜 脚本文件: 3个');
        
        console.log('\n🚀 部署步骤:');
        console.log('   1. 解压部署包到目标服务器');
        console.log('   2. 运行 scripts\\install.bat 进行安装');
        console.log('   3. 运行 start.bat 启动系统');
        console.log('   4. 访问 http://localhost:3001');
        console.log('   5. 使用 admin/admin123 登录');
        
        console.log('\n✨ V2.6新功能:');
        console.log('   🏢 公司名称全局显示');
        console.log('   🔄 设置自动更新机制');
        console.log('   ⚡ 即时生效无需刷新');
        console.log('   🌐 全局一致性保证');
        
    } else {
        console.log('❌ 部分测试失败，需要修复以下问题:');
        
        Object.entries(results).forEach(([key, passed]) => {
            if (!passed) {
                const testNames = {
                    structure: '文件结构',
                    version: '版本号更新',
                    features: 'V2.6功能',
                    documentation: '文档完整性',
                    scripts: '脚本文件'
                };
                console.log(`   ❌ ${testNames[key]}测试失败`);
            }
        });
    }
    
    return allPassed;
}

// 运行测试
runDeploymentTest().catch(error => {
    console.error('❌ 测试运行出错:', error);
    process.exit(1);
});
